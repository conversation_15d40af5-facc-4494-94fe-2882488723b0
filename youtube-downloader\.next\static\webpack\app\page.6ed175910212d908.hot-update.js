"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    _s();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoInfo, setVideoInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [downloading, setDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetInfo = async ()=>{\n        if (!url) return;\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/video-info\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    url\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get video info\");\n            }\n            setVideoInfo(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDownload = async (format, quality)=>{\n        if (!url) return;\n        setDownloading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/download\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    url,\n                    format,\n                    quality\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || \"Download failed\");\n            }\n            // Create download link\n            const blob = await response.blob();\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = downloadUrl;\n            const extension = format === \"mp3\" ? \"webm\" : \"mp4\";\n            a.download = \"\".concat((videoInfo === null || videoInfo === void 0 ? void 0 : videoInfo.title) || \"video\", \".\").concat(extension);\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(downloadUrl);\n            document.body.removeChild(a);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Download failed\");\n        } finally{\n            setDownloading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"YouTube Downloader\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                children: \"Download high-quality YouTube videos and audio files\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400 mt-2\",\n                                children: \"Audio downloads are in WebM format - use online converters to convert to MP3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    placeholder: \"Paste YouTube URL here...\",\n                                    className: \"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGetInfo,\n                                    disabled: loading || !url,\n                                    className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                    children: [\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Info\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800 dark:text-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this),\n                    videoInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: videoInfo.thumbnail,\n                                    alt: videoInfo.title,\n                                    className: \"w-full md:w-80 h-48 object-cover rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: videoInfo.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                            children: [\n                                                \"Duration: \",\n                                                videoInfo.duration\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                    children: \"Download Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                                children: \"Audio (WebM)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 172,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                                                children: \"High quality audio only - can be converted to MP3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 175,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDownload(\"mp3\"),\n                                                                disabled: downloading,\n                                                                className: \"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                                children: [\n                                                                    downloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        className: \"w-4 h-4 animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Download\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, this),\n                                                videoInfo.formats.map((format, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-6 h-6 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                                    children: [\n                                                                                        format.quality,\n                                                                                        \" (\",\n                                                                                        format.format,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 205,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                                                    children: format.hasAudio ? \"Video + Audio\" : \"Video only\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 208,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDownload(\"mp4\", format.quality),\n                                                                    disabled: downloading,\n                                                                    className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                                    children: [\n                                                                        downloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            className: \"w-4 h-4 animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 225,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"Download\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Qucz7aNz34KUsbAGh5/X91VMI1c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});