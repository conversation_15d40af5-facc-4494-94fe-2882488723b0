"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/miniget";
exports.ids = ["vendor-chunks/miniget"];
exports.modules = {

/***/ "(rsc)/./node_modules/miniget/dist/index.js":
/*!********************************************!*\
  !*** ./node_modules/miniget/dist/index.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst http_1 = __importDefault(__webpack_require__(/*! http */ \"http\"));\nconst https_1 = __importDefault(__webpack_require__(/*! https */ \"https\"));\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst httpLibs = { 'http:': http_1.default, 'https:': https_1.default };\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst retryStatusCodes = new Set([429, 503]);\n// `request`, `response`, `abort`, left out, miniget will emit these.\nconst requestEvents = ['connect', 'continue', 'information', 'socket', 'timeout', 'upgrade'];\nconst responseEvents = ['aborted'];\nMiniget.MinigetError = class MinigetError extends Error {\n    constructor(message, statusCode) {\n        super(message);\n        this.statusCode = statusCode;\n    }\n};\nMiniget.defaultOptions = {\n    maxRedirects: 10,\n    maxRetries: 2,\n    maxReconnects: 0,\n    backoff: { inc: 100, max: 10000 },\n};\nfunction Miniget(url, options = {}) {\n    var _a;\n    const opts = Object.assign({}, Miniget.defaultOptions, options);\n    const stream = new stream_1.PassThrough({ highWaterMark: opts.highWaterMark });\n    stream.destroyed = stream.aborted = false;\n    let activeRequest;\n    let activeResponse;\n    let activeDecodedStream;\n    let redirects = 0;\n    let retries = 0;\n    let retryTimeout;\n    let reconnects = 0;\n    let contentLength;\n    let acceptRanges = false;\n    let rangeStart = 0, rangeEnd;\n    let downloaded = 0;\n    // Check if this is a ranged request.\n    if ((_a = opts.headers) === null || _a === void 0 ? void 0 : _a.Range) {\n        let r = /bytes=(\\d+)-(\\d+)?/.exec(`${opts.headers.Range}`);\n        if (r) {\n            rangeStart = parseInt(r[1], 10);\n            rangeEnd = parseInt(r[2], 10);\n        }\n    }\n    // Add `Accept-Encoding` header.\n    if (opts.acceptEncoding) {\n        opts.headers = Object.assign({\n            'Accept-Encoding': Object.keys(opts.acceptEncoding).join(', '),\n        }, opts.headers);\n    }\n    const downloadHasStarted = () => activeDecodedStream && downloaded > 0;\n    const downloadComplete = () => !acceptRanges || downloaded === contentLength;\n    const reconnect = (err) => {\n        activeDecodedStream = null;\n        retries = 0;\n        let inc = opts.backoff.inc;\n        let ms = Math.min(inc, opts.backoff.max);\n        retryTimeout = setTimeout(doDownload, ms);\n        stream.emit('reconnect', reconnects, err);\n    };\n    const reconnectIfEndedEarly = (err) => {\n        if (options.method !== 'HEAD' && !downloadComplete() && reconnects++ < opts.maxReconnects) {\n            reconnect(err);\n            return true;\n        }\n        return false;\n    };\n    const retryRequest = (retryOptions) => {\n        if (stream.destroyed) {\n            return false;\n        }\n        if (downloadHasStarted()) {\n            return reconnectIfEndedEarly(retryOptions.err);\n        }\n        else if ((!retryOptions.err || retryOptions.err.message === 'ENOTFOUND') &&\n            retries++ < opts.maxRetries) {\n            let ms = retryOptions.retryAfter ||\n                Math.min(retries * opts.backoff.inc, opts.backoff.max);\n            retryTimeout = setTimeout(doDownload, ms);\n            stream.emit('retry', retries, retryOptions.err);\n            return true;\n        }\n        return false;\n    };\n    const forwardEvents = (ee, events) => {\n        for (let event of events) {\n            ee.on(event, stream.emit.bind(stream, event));\n        }\n    };\n    const doDownload = () => {\n        let parsed = {}, httpLib;\n        try {\n            let urlObj = typeof url === 'string' ? new URL(url) : url;\n            parsed = Object.assign({}, {\n                host: urlObj.host,\n                hostname: urlObj.hostname,\n                path: urlObj.pathname + urlObj.search + urlObj.hash,\n                port: urlObj.port,\n                protocol: urlObj.protocol,\n            });\n            if (urlObj.username) {\n                parsed.auth = `${urlObj.username}:${urlObj.password}`;\n            }\n            httpLib = httpLibs[String(parsed.protocol)];\n        }\n        catch (err) {\n            // Let the error be caught by the if statement below.\n        }\n        if (!httpLib) {\n            stream.emit('error', new Miniget.MinigetError(`Invalid URL: ${url}`));\n            return;\n        }\n        Object.assign(parsed, opts);\n        if (acceptRanges && downloaded > 0) {\n            let start = downloaded + rangeStart;\n            let end = rangeEnd || '';\n            parsed.headers = Object.assign({}, parsed.headers, {\n                Range: `bytes=${start}-${end}`,\n            });\n        }\n        if (opts.transform) {\n            try {\n                parsed = opts.transform(parsed);\n            }\n            catch (err) {\n                stream.emit('error', err);\n                return;\n            }\n            if (!parsed || parsed.protocol) {\n                httpLib = httpLibs[String(parsed === null || parsed === void 0 ? void 0 : parsed.protocol)];\n                if (!httpLib) {\n                    stream.emit('error', new Miniget.MinigetError('Invalid URL object from `transform` function'));\n                    return;\n                }\n            }\n        }\n        const onError = (err) => {\n            if (stream.destroyed || stream.readableEnded) {\n                return;\n            }\n            cleanup();\n            if (!retryRequest({ err })) {\n                stream.emit('error', err);\n            }\n            else {\n                activeRequest.removeListener('close', onRequestClose);\n            }\n        };\n        const onRequestClose = () => {\n            cleanup();\n            retryRequest({});\n        };\n        const cleanup = () => {\n            activeRequest.removeListener('close', onRequestClose);\n            activeResponse === null || activeResponse === void 0 ? void 0 : activeResponse.removeListener('data', onData);\n            activeDecodedStream === null || activeDecodedStream === void 0 ? void 0 : activeDecodedStream.removeListener('end', onEnd);\n        };\n        const onData = (chunk) => { downloaded += chunk.length; };\n        const onEnd = () => {\n            cleanup();\n            if (!reconnectIfEndedEarly()) {\n                stream.end();\n            }\n        };\n        activeRequest = httpLib.request(parsed, (res) => {\n            // Needed for node v10, v12.\n            // istanbul ignore next\n            if (stream.destroyed) {\n                return;\n            }\n            if (redirectStatusCodes.has(res.statusCode)) {\n                if (redirects++ >= opts.maxRedirects) {\n                    stream.emit('error', new Miniget.MinigetError('Too many redirects'));\n                }\n                else {\n                    if (res.headers.location) {\n                        url = res.headers.location;\n                    }\n                    else {\n                        let err = new Miniget.MinigetError('Redirect status code given with no location', res.statusCode);\n                        stream.emit('error', err);\n                        cleanup();\n                        return;\n                    }\n                    setTimeout(doDownload, parseInt(res.headers['retry-after'] || '0', 10) * 1000);\n                    stream.emit('redirect', url);\n                }\n                cleanup();\n                return;\n                // Check for rate limiting.\n            }\n            else if (retryStatusCodes.has(res.statusCode)) {\n                if (!retryRequest({ retryAfter: parseInt(res.headers['retry-after'] || '0', 10) })) {\n                    let err = new Miniget.MinigetError(`Status code: ${res.statusCode}`, res.statusCode);\n                    stream.emit('error', err);\n                }\n                cleanup();\n                return;\n            }\n            else if (res.statusCode && (res.statusCode < 200 || res.statusCode >= 400)) {\n                let err = new Miniget.MinigetError(`Status code: ${res.statusCode}`, res.statusCode);\n                if (res.statusCode >= 500) {\n                    onError(err);\n                }\n                else {\n                    stream.emit('error', err);\n                }\n                cleanup();\n                return;\n            }\n            activeDecodedStream = res;\n            if (opts.acceptEncoding && res.headers['content-encoding']) {\n                for (let enc of res.headers['content-encoding'].split(', ').reverse()) {\n                    let fn = opts.acceptEncoding[enc];\n                    if (fn) {\n                        activeDecodedStream = activeDecodedStream.pipe(fn());\n                        activeDecodedStream.on('error', onError);\n                    }\n                }\n            }\n            if (!contentLength) {\n                contentLength = parseInt(`${res.headers['content-length']}`, 10);\n                acceptRanges = res.headers['accept-ranges'] === 'bytes' &&\n                    contentLength > 0 && opts.maxReconnects > 0;\n            }\n            res.on('data', onData);\n            activeDecodedStream.on('end', onEnd);\n            activeDecodedStream.pipe(stream, { end: !acceptRanges });\n            activeResponse = res;\n            stream.emit('response', res);\n            res.on('error', onError);\n            forwardEvents(res, responseEvents);\n        });\n        activeRequest.on('error', onError);\n        activeRequest.on('close', onRequestClose);\n        forwardEvents(activeRequest, requestEvents);\n        if (stream.destroyed) {\n            streamDestroy(...destroyArgs);\n        }\n        stream.emit('request', activeRequest);\n        activeRequest.end();\n    };\n    stream.abort = (err) => {\n        console.warn('`MinigetStream#abort()` has been deprecated in favor of `MinigetStream#destroy()`');\n        stream.aborted = true;\n        stream.emit('abort');\n        stream.destroy(err);\n    };\n    let destroyArgs = [];\n    const streamDestroy = (err) => {\n        activeRequest.destroy(err);\n        activeDecodedStream === null || activeDecodedStream === void 0 ? void 0 : activeDecodedStream.unpipe(stream);\n        activeDecodedStream === null || activeDecodedStream === void 0 ? void 0 : activeDecodedStream.destroy();\n        clearTimeout(retryTimeout);\n    };\n    stream._destroy = (...args) => {\n        stream.destroyed = true;\n        if (activeRequest) {\n            streamDestroy(...args);\n        }\n        else {\n            destroyArgs = args;\n        }\n    };\n    stream.text = () => new Promise((resolve, reject) => {\n        let body = '';\n        stream.setEncoding('utf8');\n        stream.on('data', chunk => body += chunk);\n        stream.on('end', () => resolve(body));\n        stream.on('error', reject);\n    });\n    process.nextTick(doDownload);\n    return stream;\n}\nmodule.exports = Miniget;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/miniget/dist/index.js\n");

/***/ })

};
;