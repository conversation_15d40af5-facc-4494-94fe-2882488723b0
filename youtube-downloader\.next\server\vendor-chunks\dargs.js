"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dargs";
exports.ids = ["vendor-chunks/dargs"];
exports.modules = {

/***/ "(rsc)/./node_modules/dargs/index.js":
/*!*************************************!*\
  !*** ./node_modules/dargs/index.js ***!
  \*************************************/
/***/ ((module) => {

eval("\n\nconst match = (array, value) =>\n\tarray.some(x => (x instanceof RegExp ? x.test(value) : x === value));\n\nconst dargs = (object, options) => {\n\tconst arguments_ = [];\n\tlet extraArguments = [];\n\tlet separatedArguments = [];\n\n\toptions = {\n\t\tuseEquals: true,\n\t\tshortFlag: true,\n\t\t...options\n\t};\n\n\tconst makeArguments = (key, value) => {\n\t\tconst prefix = options.shortFlag && key.length === 1 ? '-' : '--';\n\t\tconst theKey = (options.allowCamelCase ?\n\t\t\tkey :\n\t\t\tkey.replace(/[A-Z]/g, '-$&').toLowerCase());\n\n\t\tkey = prefix + theKey;\n\n\t\tif (options.useEquals) {\n\t\t\targuments_.push(key + (value ? `=${value}` : ''));\n\t\t} else {\n\t\t\targuments_.push(key);\n\n\t\t\tif (value) {\n\t\t\t\targuments_.push(value);\n\t\t\t}\n\t\t}\n\t};\n\n\tconst makeAliasArg = (key, value) => {\n\t\targuments_.push(`-${key}`);\n\n\t\tif (value) {\n\t\t\targuments_.push(value);\n\t\t}\n\t};\n\n\tfor (let [key, value] of Object.entries(object)) {\n\t\tlet pushArguments = makeArguments;\n\n\t\tif (Array.isArray(options.excludes) && match(options.excludes, key)) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (Array.isArray(options.includes) && !match(options.includes, key)) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeof options.aliases === 'object' && options.aliases[key]) {\n\t\t\tkey = options.aliases[key];\n\t\t\tpushArguments = makeAliasArg;\n\t\t}\n\n\t\tif (key === '--') {\n\t\t\tif (!Array.isArray(value)) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t`Expected key \\`--\\` to be Array, got ${typeof value}`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tseparatedArguments = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (key === '_') {\n\t\t\tif (!Array.isArray(value)) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t`Expected key \\`_\\` to be Array, got ${typeof value}`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\textraArguments = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (value === true) {\n\t\t\tpushArguments(key, '');\n\t\t}\n\n\t\tif (value === false && !options.ignoreFalse) {\n\t\t\tpushArguments(`no-${key}`);\n\t\t}\n\n\t\tif (typeof value === 'string') {\n\t\t\tpushArguments(key, value);\n\t\t}\n\n\t\tif (typeof value === 'number' && !Number.isNaN(value)) {\n\t\t\tpushArguments(key, String(value));\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\tfor (const arrayValue of value) {\n\t\t\t\tpushArguments(key, arrayValue);\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (const argument of extraArguments) {\n\t\targuments_.push(String(argument));\n\t}\n\n\tif (separatedArguments.length > 0) {\n\t\targuments_.push('--');\n\t}\n\n\tfor (const argument of separatedArguments) {\n\t\targuments_.push(String(argument));\n\t}\n\n\treturn arguments_;\n};\n\nmodule.exports = dargs;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dargs/index.js\n");

/***/ })

};
;