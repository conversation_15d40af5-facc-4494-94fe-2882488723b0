import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { url, format, quality } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // For demo purposes, return a message indicating this is a demo
    // In a production environment, you would implement actual downloading with yt-dlp
    const message = `Demo: Would download ${
      format === "mp3" ? "audio" : "video"
    } ${quality ? `in ${quality}` : ""} from ${url}`;

    // Create a simple text file as a demo download
    const content = `YouTube Downloader Demo\n\n${message}\n\nThis is a demonstration of the YouTube downloader interface.\nIn a production environment, this would download the actual ${
      format === "mp3" ? "audio" : "video"
    } file.\n\nTo implement real downloading, you would need to:\n1. Install yt-dlp on your server\n2. Use child_process to execute yt-dlp commands\n3. Stream the downloaded file back to the client\n\nURL: ${url}\nFormat: ${format}\nQuality: ${
      quality || "default"
    }`;

    const buffer = Buffer.from(content, "utf-8");
    const filename = `demo-${
      format === "mp3" ? "audio" : "video"
    }-${Date.now()}.txt`;

    return new NextResponse(buffer, {
      headers: {
        "Content-Type": "text/plain",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Error in download demo:", error);
    return NextResponse.json({ error: "Download failed" }, { status: 500 });
  }
}
