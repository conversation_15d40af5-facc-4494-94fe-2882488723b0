import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { Readable } from "stream";

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { url, format, quality } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // Get video info first to get the title
    const infoCommand = `yt-dlp --dump-single-json --no-check-certificates --no-warnings "${url}"`;
    const { stdout } = await execAsync(infoCommand);
    const info = JSON.parse(stdout);

    const title = (info.title || "video").replace(/[^\w\s-]/g, "").trim();

    if (format === "mp3") {
      // Download audio only and convert to MP3
      const audioCommand = `yt-dlp --extract-audio --audio-format mp3 --audio-quality 0 --no-check-certificates --no-warnings -o - "${url}"`;

      return new Promise((resolve, reject) => {
        const child = exec(audioCommand, {
          encoding: "buffer",
          maxBuffer: 1024 * 1024 * 100,
        }); // 100MB buffer
        const chunks: Buffer[] = [];

        child.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        child.on("close", (code) => {
          if (code === 0) {
            const buffer = Buffer.concat(chunks);
            const response = new NextResponse(buffer, {
              headers: {
                "Content-Type": "audio/mpeg",
                "Content-Disposition": `attachment; filename="${title}.mp3"`,
              },
            });
            resolve(response);
          } else {
            reject(
              NextResponse.json(
                { error: "Audio download failed" },
                { status: 500 }
              )
            );
          }
        });

        child.on("error", (err) => {
          console.error("Audio download error:", err);
          reject(
            NextResponse.json(
              { error: "Audio download failed" },
              { status: 500 }
            )
          );
        });
      });
    } else {
      // Download video with audio
      let formatSelector;

      if (quality) {
        const height = quality.replace("p", "");

        if (parseInt(height) <= 360) {
          // For 360p and below, use format 18 which has both video and audio
          formatSelector = "18/best[height<=360]";
        } else {
          // For higher qualities, be more specific about format selection
          switch (height) {
            case "1080":
              formatSelector =
                "137+140/best[height<=1080]+bestaudio/best[height<=1080]";
              break;
            case "720":
              formatSelector =
                "136+140/best[height<=720]+bestaudio/best[height<=720]";
              break;
            case "480":
              formatSelector =
                "135+140/best[height<=480]+bestaudio/best[height<=480]";
              break;
            default:
              formatSelector = `best[height<=${height}]+bestaudio/best[height<=${height}]`;
          }
        }
      } else {
        // Default: best quality with audio
        formatSelector = "best+bestaudio/best";
      }

      const videoCommand = `yt-dlp -f "${formatSelector}" --merge-output-format mp4 --no-check-certificates --no-warnings -o - "${url}"`;

      return new Promise((resolve, reject) => {
        const child = exec(videoCommand, {
          encoding: "buffer",
          maxBuffer: 1024 * 1024 * 200,
        }); // 200MB buffer
        const chunks: Buffer[] = [];

        child.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        child.on("close", (code) => {
          if (code === 0) {
            const buffer = Buffer.concat(chunks);
            const response = new NextResponse(buffer, {
              headers: {
                "Content-Type": "video/mp4",
                "Content-Disposition": `attachment; filename="${title}.mp4"`,
              },
            });
            resolve(response);
          } else {
            reject(
              NextResponse.json(
                { error: "Video download failed" },
                { status: 500 }
              )
            );
          }
        });

        child.on("error", (err) => {
          console.error("Video download error:", err);
          reject(
            NextResponse.json(
              { error: "Video download failed" },
              { status: 500 }
            )
          );
        });
      });
    }
  } catch (error) {
    console.error("Error downloading:", error);
    return NextResponse.json({ error: "Download failed" }, { status: 500 });
  }
}
