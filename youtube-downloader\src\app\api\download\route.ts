import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { Readable } from "stream";

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { url, format, quality } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // Get video info first to get the title
    const infoCommand = `yt-dlp --dump-single-json --no-check-certificates --no-warnings "${url}"`;
    const { stdout } = await execAsync(infoCommand);
    const info = JSON.parse(stdout);

    const title = (info.title || "video").replace(/[^\w\s-]/g, "").trim();

    if (format === "mp3") {
      // Download audio only and convert to MP3
      const audioCommand = `yt-dlp --extract-audio --audio-format mp3 --audio-quality 0 --no-check-certificates --no-warnings -o - "${url}"`;

      return new Promise((resolve, reject) => {
        const child = exec(audioCommand, {
          encoding: "buffer",
          maxBuffer: 1024 * 1024 * 100,
        }); // 100MB buffer
        const chunks: Buffer[] = [];

        child.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        child.on("close", (code) => {
          if (code === 0) {
            const buffer = Buffer.concat(chunks);
            const response = new NextResponse(buffer, {
              headers: {
                "Content-Type": "audio/mpeg",
                "Content-Disposition": `attachment; filename="${title}.mp3"`,
              },
            });
            resolve(response);
          } else {
            reject(
              NextResponse.json(
                { error: "Audio download failed" },
                { status: 500 }
              )
            );
          }
        });

        child.on("error", (err) => {
          console.error("Audio download error:", err);
          reject(
            NextResponse.json(
              { error: "Audio download failed" },
              { status: 500 }
            )
          );
        });
      });
    } else {
      // Download video
      let formatSelector = "best[ext=mp4]";

      if (quality) {
        const height = quality.replace("p", "");
        formatSelector = `best[height<=${height}][ext=mp4]/best[ext=mp4]`;
      }

      const videoCommand = `yt-dlp -f "${formatSelector}" --no-check-certificates --no-warnings -o - "${url}"`;

      return new Promise((resolve, reject) => {
        const child = exec(videoCommand, {
          encoding: "buffer",
          maxBuffer: 1024 * 1024 * 200,
        }); // 200MB buffer
        const chunks: Buffer[] = [];

        child.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        child.on("close", (code) => {
          if (code === 0) {
            const buffer = Buffer.concat(chunks);
            const response = new NextResponse(buffer, {
              headers: {
                "Content-Type": "video/mp4",
                "Content-Disposition": `attachment; filename="${title}.mp4"`,
              },
            });
            resolve(response);
          } else {
            reject(
              NextResponse.json(
                { error: "Video download failed" },
                { status: 500 }
              )
            );
          }
        });

        child.on("error", (err) => {
          console.error("Video download error:", err);
          reject(
            NextResponse.json(
              { error: "Video download failed" },
              { status: 500 }
            )
          );
        });
      });
    }
  } catch (error) {
    console.error("Error downloading:", error);
    return NextResponse.json({ error: "Download failed" }, { status: 500 });
  }
}
