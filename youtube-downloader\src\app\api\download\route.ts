import { NextRequest, NextResponse } from "next/server";
import youtubedl from "youtube-dl-exec";
import { Readable } from "stream";

export async function POST(request: NextRequest) {
  try {
    const { url, format, quality } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // Get video info first to get the title
    const info = await youtubedl(url, {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
    });

    const title = (info.title || "video").replace(/[^\w\s-]/g, "").trim();

    if (format === "mp3") {
      // Download audio only
      const stream = youtubedl.exec(url, {
        extractAudio: true,
        audioFormat: "mp3",
        audioQuality: 0, // best quality
        output: "-", // output to stdout
        noCheckCertificates: true,
        noWarnings: true,
        addHeader: ["referer:youtube.com", "user-agent:googlebot"],
      });

      return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];

        stream.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        stream.stdout?.on("end", () => {
          const buffer = Buffer.concat(chunks);
          const response = new NextResponse(buffer, {
            headers: {
              "Content-Type": "audio/mpeg",
              "Content-Disposition": `attachment; filename="${title}.mp3"`,
            },
          });
          resolve(response);
        });

        stream.on("error", (err) => {
          console.error("Audio download error:", err);
          reject(
            NextResponse.json(
              { error: "Audio download failed" },
              { status: 500 }
            )
          );
        });
      });
    } else {
      // Download video
      let formatSelector = "best[ext=mp4]";

      if (quality) {
        const height = quality.replace("p", "");
        formatSelector = `best[height<=${height}][ext=mp4]/best[ext=mp4]`;
      }

      const stream = youtubedl.exec(url, {
        format: formatSelector,
        output: "-", // output to stdout
        noCheckCertificates: true,
        noWarnings: true,
        addHeader: ["referer:youtube.com", "user-agent:googlebot"],
      });

      return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];

        stream.stdout?.on("data", (chunk: Buffer) => {
          chunks.push(chunk);
        });

        stream.stdout?.on("end", () => {
          const buffer = Buffer.concat(chunks);
          const response = new NextResponse(buffer, {
            headers: {
              "Content-Type": "video/mp4",
              "Content-Disposition": `attachment; filename="${title}.mp4"`,
            },
          });
          resolve(response);
        });

        stream.on("error", (err) => {
          console.error("Video download error:", err);
          reject(
            NextResponse.json(
              { error: "Video download failed" },
              { status: 500 }
            )
          );
        });
      });
    }
  } catch (error) {
    console.error("Error downloading:", error);
    return NextResponse.json({ error: "Download failed" }, { status: 500 });
  }
}
