"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/youtube-dl-exec";
exports.ids = ["vendor-chunks/youtube-dl-exec"];
exports.modules = {

/***/ "(rsc)/./node_modules/youtube-dl-exec/src/constants.js":
/*!*******************************************************!*\
  !*** ./node_modules/youtube-dl-exec/src/constants.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst isUnix = __webpack_require__(/*! is-unix */ \"(rsc)/./node_modules/is-unix/index.js\")\nconst path = __webpack_require__(/*! path */ \"path\")\n\nconst PLATFORM_WIN = 'win32'\nconst PLATFORM_UNIX = 'unix'\n\nconst YOUTUBE_DL_HOST =\n  process.env.YOUTUBE_DL_HOST ??\n  'https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest'\n\nconst YOUTUBE_DL_DIR =\n  process.env.YOUTUBE_DL_DIR ?? path.join(__dirname, '..', 'bin')\n\nconst YOUTUBE_DL_PLATFORM =\n  process.env.YOUTUBE_DL_PLATFORM ?? isUnix(process.platform)\n    ? PLATFORM_UNIX\n    : PLATFORM_WIN\n\nconst YOUTUBE_DL_FILENAME = process.env.YOUTUBE_DL_FILENAME || 'yt-dlp'\n\nconst YOUTUBE_DL_FILE =\n  !YOUTUBE_DL_FILENAME.endsWith('.exe') && YOUTUBE_DL_PLATFORM === 'win32'\n    ? `${YOUTUBE_DL_FILENAME}.exe`\n    : YOUTUBE_DL_FILENAME\n\nconst YOUTUBE_DL_PATH = path.join(YOUTUBE_DL_DIR, YOUTUBE_DL_FILE)\n\nconst YOUTUBE_DL_SKIP_DOWNLOAD = process.env.YOUTUBE_DL_SKIP_DOWNLOAD\n\nconst GITHUB_TOKEN = process.env.GITHUB_TOKEN || process.env.GH_TOKEN\n\nmodule.exports = {\n  GITHUB_TOKEN,\n  YOUTUBE_DL_DIR,\n  YOUTUBE_DL_FILE,\n  YOUTUBE_DL_FILENAME,\n  YOUTUBE_DL_HOST,\n  YOUTUBE_DL_PATH,\n  YOUTUBE_DL_PLATFORM,\n  YOUTUBE_DL_SKIP_DOWNLOAD\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/youtube-dl-exec/src/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/youtube-dl-exec/src/index.js":
/*!***************************************************!*\
  !*** ./node_modules/youtube-dl-exec/src/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst dargs = __webpack_require__(/*! dargs */ \"(rsc)/./node_modules/dargs/index.js\")\nconst $ = __webpack_require__(/*! tinyspawn */ \"(rsc)/./node_modules/tinyspawn/src/index.js\")\n\nconst constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/youtube-dl-exec/src/constants.js\")\n\nconst args = (flags = {}) => dargs(flags, { useEquals: false }).filter(Boolean)\n\nconst isJSON = (str = '') => str.startsWith('{')\n\nconst parse = ({ stdout, stderr, ...details }) => {\n  if (details.exitCode === 0) { return isJSON(stdout) ? JSON.parse(stdout) : stdout }\n  throw Object.assign(new Error(stderr), { stderr, stdout }, details)\n}\n\nconst create = binaryPath => {\n  const fn = (...args) =>\n    fn\n      .exec(...args)\n      .then(parse)\n      .catch(parse)\n  fn.exec = (url, flags, opts) => $(binaryPath, [url].concat(args(flags)), opts)\n  return fn\n}\n\nconst defaultInstance = create(constants.YOUTUBE_DL_PATH)\n\nmodule.exports = defaultInstance\nmodule.exports.youtubeDl = defaultInstance\nmodule.exports.create = create\nmodule.exports.args = args\nmodule.exports.isJSON = isJSON\nmodule.exports.constants = constants\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/youtube-dl-exec/src/index.js\n");

/***/ })

};
;