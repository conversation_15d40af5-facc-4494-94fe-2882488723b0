/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-info/route";
exports.ids = ["app/api/video-info/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/video-info/route.ts */ \"(rsc)/./src/app/api/video-info/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-info/route\",\n        pathname: \"/api/video-info\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-info/route\"\n    },\n    resolvedPagePath: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\api\\\\video-info\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/video-info/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/video-info/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\nasync function POST(request) {\n    try {\n        const { url } = await request.json();\n        if (!url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"URL is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Basic YouTube URL validation\n        const youtubeRegex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/)[\\w-]+/;\n        if (!youtubeRegex.test(url)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid YouTube URL\"\n            }, {\n                status: 400\n            });\n        }\n        // Get video info using yt-dlp command directly\n        const command = `yt-dlp --dump-single-json --no-check-certificates --no-warnings \"${url}\"`;\n        const { stdout } = await execAsync(command);\n        const info = JSON.parse(stdout);\n        // Extract available formats\n        const formats = info.formats?.filter((format)=>format.vcodec !== \"none\" && format.ext === \"mp4\").map((format)=>({\n                quality: format.height ? `${format.height}p` : format.format_note || \"Unknown\",\n                format: format.ext || \"mp4\",\n                hasAudio: format.acodec !== \"none\",\n                format_id: format.format_id\n            })).filter((format, index, self)=>index === self.findIndex((f)=>f.quality === format.quality)).sort((a, b)=>{\n            const aHeight = parseInt(a.quality) || 0;\n            const bHeight = parseInt(b.quality) || 0;\n            return bHeight - aHeight;\n        }) || [];\n        // Format duration\n        const duration = info.duration ? `${Math.floor(info.duration / 60)}:${(info.duration % 60).toString().padStart(2, \"0\")}` : \"Unknown\";\n        const videoInfo = {\n            title: info.title || \"Unknown Title\",\n            duration,\n            thumbnail: info.thumbnail || \"\",\n            formats\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(videoInfo);\n    } catch (error) {\n        console.error(\"Error getting video info:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get video information. Please check the URL and try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS92aWRlby1pbmZvL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3RDtBQUNuQjtBQUNKO0FBRWpDLE1BQU1HLFlBQVlELCtDQUFTQSxDQUFDRCwrQ0FBSUE7QUFFekIsZUFBZUcsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU0sRUFBRUMsR0FBRyxFQUFFLEdBQUcsTUFBTUQsUUFBUUUsSUFBSTtRQUVsQyxJQUFJLENBQUNELEtBQUs7WUFDUixPQUFPTixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWtCLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN2RTtRQUVBLCtCQUErQjtRQUMvQixNQUFNQyxlQUNKO1FBQ0YsSUFBSSxDQUFDQSxhQUFhQyxJQUFJLENBQUNMLE1BQU07WUFDM0IsT0FBT04scURBQVlBLENBQUNPLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBc0IsR0FDL0I7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLCtDQUErQztRQUMvQyxNQUFNRyxVQUFVLENBQUMsaUVBQWlFLEVBQUVOLElBQUksQ0FBQyxDQUFDO1FBQzFGLE1BQU0sRUFBRU8sTUFBTSxFQUFFLEdBQUcsTUFBTVYsVUFBVVM7UUFDbkMsTUFBTUUsT0FBT0MsS0FBS0MsS0FBSyxDQUFDSDtRQUV4Qiw0QkFBNEI7UUFDNUIsTUFBTUksVUFDSkgsS0FBS0csT0FBTyxFQUNSQyxPQUNBLENBQUNDLFNBQWdCQSxPQUFPQyxNQUFNLEtBQUssVUFBVUQsT0FBT0UsR0FBRyxLQUFLLE9BRTdEQyxJQUFJLENBQUNILFNBQWlCO2dCQUNyQkksU0FBU0osT0FBT0ssTUFBTSxHQUNsQixHQUFHTCxPQUFPSyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQ25CTCxPQUFPTSxXQUFXLElBQUk7Z0JBQzFCTixRQUFRQSxPQUFPRSxHQUFHLElBQUk7Z0JBQ3RCSyxVQUFVUCxPQUFPUSxNQUFNLEtBQUs7Z0JBQzVCQyxXQUFXVCxPQUFPUyxTQUFTO1lBQzdCLElBQ0NWLE9BQ0MsQ0FBQ0MsUUFBYVUsT0FBZUMsT0FDM0JELFVBQVVDLEtBQUtDLFNBQVMsQ0FBQyxDQUFDQyxJQUFXQSxFQUFFVCxPQUFPLEtBQUtKLE9BQU9JLE9BQU8sR0FFcEVVLEtBQUssQ0FBQ0MsR0FBUUM7WUFDYixNQUFNQyxVQUFVQyxTQUFTSCxFQUFFWCxPQUFPLEtBQUs7WUFDdkMsTUFBTWUsVUFBVUQsU0FBU0YsRUFBRVosT0FBTyxLQUFLO1lBQ3ZDLE9BQU9lLFVBQVVGO1FBQ25CLE1BQU0sRUFBRTtRQUVaLGtCQUFrQjtRQUNsQixNQUFNRyxXQUFXekIsS0FBS3lCLFFBQVEsR0FDMUIsR0FBR0MsS0FBS0MsS0FBSyxDQUFDM0IsS0FBS3lCLFFBQVEsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDekIsS0FBS3lCLFFBQVEsR0FBRyxFQUFDLEVBQ3BERyxRQUFRLEdBQ1JDLFFBQVEsQ0FBQyxHQUFHLE1BQU0sR0FDckI7UUFFSixNQUFNQyxZQUFZO1lBQ2hCQyxPQUFPL0IsS0FBSytCLEtBQUssSUFBSTtZQUNyQk47WUFDQU8sV0FBV2hDLEtBQUtnQyxTQUFTLElBQUk7WUFDN0I3QjtRQUNGO1FBRUEsT0FBT2pCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUNxQztJQUMzQixFQUFFLE9BQU9wQyxPQUFPO1FBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBT1IscURBQVlBLENBQUNPLElBQUksQ0FDdEI7WUFDRUMsT0FDRTtRQUNKLEdBQ0E7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxZb3VUdWJlIERvd25sb2FkZXJcXHlvdXR1YmUtZG93bmxvYWRlclxcc3JjXFxhcHBcXGFwaVxcdmlkZW8taW5mb1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHsgZXhlYyB9IGZyb20gXCJjaGlsZF9wcm9jZXNzXCI7XG5pbXBvcnQgeyBwcm9taXNpZnkgfSBmcm9tIFwidXRpbFwiO1xuXG5jb25zdCBleGVjQXN5bmMgPSBwcm9taXNpZnkoZXhlYyk7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyB1cmwgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgaWYgKCF1cmwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIlVSTCBpcyByZXF1aXJlZFwiIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgLy8gQmFzaWMgWW91VHViZSBVUkwgdmFsaWRhdGlvblxuICAgIGNvbnN0IHlvdXR1YmVSZWdleCA9XG4gICAgICAvXihodHRwcz86XFwvXFwvKT8od3d3XFwuKT8oeW91dHViZVxcLmNvbVxcL3dhdGNoXFw/dj18eW91dHVcXC5iZVxcLylbXFx3LV0rLztcbiAgICBpZiAoIXlvdXR1YmVSZWdleC50ZXN0KHVybCkpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogXCJJbnZhbGlkIFlvdVR1YmUgVVJMXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEdldCB2aWRlbyBpbmZvIHVzaW5nIHl0LWRscCBjb21tYW5kIGRpcmVjdGx5XG4gICAgY29uc3QgY29tbWFuZCA9IGB5dC1kbHAgLS1kdW1wLXNpbmdsZS1qc29uIC0tbm8tY2hlY2stY2VydGlmaWNhdGVzIC0tbm8td2FybmluZ3MgXCIke3VybH1cImA7XG4gICAgY29uc3QgeyBzdGRvdXQgfSA9IGF3YWl0IGV4ZWNBc3luYyhjb21tYW5kKTtcbiAgICBjb25zdCBpbmZvID0gSlNPTi5wYXJzZShzdGRvdXQpO1xuXG4gICAgLy8gRXh0cmFjdCBhdmFpbGFibGUgZm9ybWF0c1xuICAgIGNvbnN0IGZvcm1hdHMgPVxuICAgICAgaW5mby5mb3JtYXRzXG4gICAgICAgID8uZmlsdGVyKFxuICAgICAgICAgIChmb3JtYXQ6IGFueSkgPT4gZm9ybWF0LnZjb2RlYyAhPT0gXCJub25lXCIgJiYgZm9ybWF0LmV4dCA9PT0gXCJtcDRcIlxuICAgICAgICApXG4gICAgICAgIC5tYXAoKGZvcm1hdDogYW55KSA9PiAoe1xuICAgICAgICAgIHF1YWxpdHk6IGZvcm1hdC5oZWlnaHRcbiAgICAgICAgICAgID8gYCR7Zm9ybWF0LmhlaWdodH1wYFxuICAgICAgICAgICAgOiBmb3JtYXQuZm9ybWF0X25vdGUgfHwgXCJVbmtub3duXCIsXG4gICAgICAgICAgZm9ybWF0OiBmb3JtYXQuZXh0IHx8IFwibXA0XCIsXG4gICAgICAgICAgaGFzQXVkaW86IGZvcm1hdC5hY29kZWMgIT09IFwibm9uZVwiLFxuICAgICAgICAgIGZvcm1hdF9pZDogZm9ybWF0LmZvcm1hdF9pZCxcbiAgICAgICAgfSkpXG4gICAgICAgIC5maWx0ZXIoXG4gICAgICAgICAgKGZvcm1hdDogYW55LCBpbmRleDogbnVtYmVyLCBzZWxmOiBhbnlbXSkgPT5cbiAgICAgICAgICAgIGluZGV4ID09PSBzZWxmLmZpbmRJbmRleCgoZjogYW55KSA9PiBmLnF1YWxpdHkgPT09IGZvcm1hdC5xdWFsaXR5KVxuICAgICAgICApXG4gICAgICAgIC5zb3J0KChhOiBhbnksIGI6IGFueSkgPT4ge1xuICAgICAgICAgIGNvbnN0IGFIZWlnaHQgPSBwYXJzZUludChhLnF1YWxpdHkpIHx8IDA7XG4gICAgICAgICAgY29uc3QgYkhlaWdodCA9IHBhcnNlSW50KGIucXVhbGl0eSkgfHwgMDtcbiAgICAgICAgICByZXR1cm4gYkhlaWdodCAtIGFIZWlnaHQ7XG4gICAgICAgIH0pIHx8IFtdO1xuXG4gICAgLy8gRm9ybWF0IGR1cmF0aW9uXG4gICAgY29uc3QgZHVyYXRpb24gPSBpbmZvLmR1cmF0aW9uXG4gICAgICA/IGAke01hdGguZmxvb3IoaW5mby5kdXJhdGlvbiAvIDYwKX06JHsoaW5mby5kdXJhdGlvbiAlIDYwKVxuICAgICAgICAgIC50b1N0cmluZygpXG4gICAgICAgICAgLnBhZFN0YXJ0KDIsIFwiMFwiKX1gXG4gICAgICA6IFwiVW5rbm93blwiO1xuXG4gICAgY29uc3QgdmlkZW9JbmZvID0ge1xuICAgICAgdGl0bGU6IGluZm8udGl0bGUgfHwgXCJVbmtub3duIFRpdGxlXCIsXG4gICAgICBkdXJhdGlvbixcbiAgICAgIHRodW1ibmFpbDogaW5mby50aHVtYm5haWwgfHwgXCJcIixcbiAgICAgIGZvcm1hdHMsXG4gICAgfTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih2aWRlb0luZm8pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZXR0aW5nIHZpZGVvIGluZm86XCIsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7XG4gICAgICAgIGVycm9yOlxuICAgICAgICAgIFwiRmFpbGVkIHRvIGdldCB2aWRlbyBpbmZvcm1hdGlvbi4gUGxlYXNlIGNoZWNrIHRoZSBVUkwgYW5kIHRyeSBhZ2Fpbi5cIixcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZXhlYyIsInByb21pc2lmeSIsImV4ZWNBc3luYyIsIlBPU1QiLCJyZXF1ZXN0IiwidXJsIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwieW91dHViZVJlZ2V4IiwidGVzdCIsImNvbW1hbmQiLCJzdGRvdXQiLCJpbmZvIiwiSlNPTiIsInBhcnNlIiwiZm9ybWF0cyIsImZpbHRlciIsImZvcm1hdCIsInZjb2RlYyIsImV4dCIsIm1hcCIsInF1YWxpdHkiLCJoZWlnaHQiLCJmb3JtYXRfbm90ZSIsImhhc0F1ZGlvIiwiYWNvZGVjIiwiZm9ybWF0X2lkIiwiaW5kZXgiLCJzZWxmIiwiZmluZEluZGV4IiwiZiIsInNvcnQiLCJhIiwiYiIsImFIZWlnaHQiLCJwYXJzZUludCIsImJIZWlnaHQiLCJkdXJhdGlvbiIsIk1hdGgiLCJmbG9vciIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJ2aWRlb0luZm8iLCJ0aXRsZSIsInRodW1ibmFpbCIsImNvbnNvbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/video-info/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();