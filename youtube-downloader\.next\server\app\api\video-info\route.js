/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-info/route";
exports.ids = ["app/api/video-info/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/video-info/route.ts */ \"(rsc)/./src/app/api/video-info/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-info/route\",\n        pathname: \"/api/video-info\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-info/route\"\n    },\n    resolvedPagePath: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\api\\\\video-info\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/video-info/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/video-info/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\nasync function POST(request) {\n    try {\n        const { url } = await request.json();\n        if (!url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"URL is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Basic YouTube URL validation\n        const youtubeRegex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/)[\\w-]+/;\n        if (!youtubeRegex.test(url)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid YouTube URL\"\n            }, {\n                status: 400\n            });\n        }\n        // Get video info using yt-dlp command directly\n        const command = `yt-dlp --dump-single-json --no-check-certificates --no-warnings \"${url}\"`;\n        const { stdout } = await execAsync(command);\n        const info = JSON.parse(stdout);\n        // Extract available formats - focus on common video qualities\n        const commonQualities = [\n            {\n                quality: \"1080p\",\n                hasAudio: false,\n                note: \"High quality (video + audio merged)\"\n            },\n            {\n                quality: \"720p\",\n                hasAudio: false,\n                note: \"HD quality (video + audio merged)\"\n            },\n            {\n                quality: \"480p\",\n                hasAudio: false,\n                note: \"Standard quality (video + audio merged)\"\n            },\n            {\n                quality: \"360p\",\n                hasAudio: true,\n                note: \"Good quality with audio\"\n            },\n            {\n                quality: \"240p\",\n                hasAudio: false,\n                note: \"Low quality (video + audio merged)\"\n            }\n        ];\n        // Check which qualities are actually available\n        const availableFormats = info.formats || [];\n        const formats = commonQualities.filter((quality)=>{\n            const height = parseInt(quality.quality);\n            return availableFormats.some((format)=>format.height && format.height >= height && format.vcodec !== \"none\");\n        }).map((quality)=>({\n                quality: quality.quality,\n                format: \"mp4\",\n                hasAudio: quality.quality === \"360p\"\n            }));\n        // Format duration\n        const duration = info.duration ? `${Math.floor(info.duration / 60)}:${(info.duration % 60).toString().padStart(2, \"0\")}` : \"Unknown\";\n        const videoInfo = {\n            title: info.title || \"Unknown Title\",\n            duration,\n            thumbnail: info.thumbnail || \"\",\n            formats\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(videoInfo);\n    } catch (error) {\n        console.error(\"Error getting video info:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get video information. Please check the URL and try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/video-info/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();