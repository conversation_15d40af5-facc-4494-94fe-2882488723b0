/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-info/route";
exports.ids = ["app/api/video-info/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/video-info/route.ts */ \"(rsc)/./src/app/api/video-info/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-info/route\",\n        pathname: \"/api/video-info\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-info/route\"\n    },\n    resolvedPagePath: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\api\\\\video-info\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_YouTube_Downloader_youtube_downloader_src_app_api_video_info_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ2aWRlby1pbmZvJTJGcm91dGUmcGFnZT0lMkZhcGklMkZ2aWRlby1pbmZvJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdmlkZW8taW5mbyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDWW91VHViZSUyMERvd25sb2FkZXIlNUN5b3V0dWJlLWRvd25sb2FkZXIlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNZb3VUdWJlJTIwRG93bmxvYWRlciU1Q3lvdXR1YmUtZG93bmxvYWRlciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDK0I7QUFDNUc7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkQ6XFxcXFlvdVR1YmUgRG93bmxvYWRlclxcXFx5b3V0dWJlLWRvd25sb2FkZXJcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdmlkZW8taW5mb1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvdmlkZW8taW5mby9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3ZpZGVvLWluZm9cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3ZpZGVvLWluZm8vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJEOlxcXFxZb3VUdWJlIERvd25sb2FkZXJcXFxceW91dHViZS1kb3dubG9hZGVyXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHZpZGVvLWluZm9cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/video-info/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/video-info/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var youtube_dl_exec__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! youtube-dl-exec */ \"(rsc)/./node_modules/youtube-dl-exec/src/index.js\");\n/* harmony import */ var youtube_dl_exec__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(youtube_dl_exec__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function POST(request) {\n    try {\n        const { url } = await request.json();\n        if (!url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"URL is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Basic YouTube URL validation\n        const youtubeRegex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/)[\\w-]+/;\n        if (!youtubeRegex.test(url)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid YouTube URL\"\n            }, {\n                status: 400\n            });\n        }\n        // Get video info using yt-dlp\n        const info = await youtube_dl_exec__WEBPACK_IMPORTED_MODULE_1___default()(url, {\n            dumpSingleJson: true,\n            noCheckCertificates: true,\n            noWarnings: true,\n            preferFreeFormats: true,\n            addHeader: [\n                \"referer:youtube.com\",\n                \"user-agent:googlebot\"\n            ]\n        }, {\n            binaryPath: \"yt-dlp\"\n        });\n        // Extract available formats\n        const formats = info.formats?.filter((format)=>format.vcodec !== \"none\" && format.ext === \"mp4\").map((format)=>({\n                quality: format.height ? `${format.height}p` : format.format_note || \"Unknown\",\n                format: format.ext || \"mp4\",\n                hasAudio: format.acodec !== \"none\",\n                format_id: format.format_id\n            })).filter((format, index, self)=>index === self.findIndex((f)=>f.quality === format.quality)).sort((a, b)=>{\n            const aHeight = parseInt(a.quality) || 0;\n            const bHeight = parseInt(b.quality) || 0;\n            return bHeight - aHeight;\n        }) || [];\n        // Format duration\n        const duration = info.duration ? `${Math.floor(info.duration / 60)}:${(info.duration % 60).toString().padStart(2, \"0\")}` : \"Unknown\";\n        const videoInfo = {\n            title: info.title || \"Unknown Title\",\n            duration,\n            thumbnail: info.thumbnail || \"\",\n            formats\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(videoInfo);\n    } catch (error) {\n        console.error(\"Error getting video info:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get video information. Please check the URL and try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/video-info/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/youtube-dl-exec","vendor-chunks/tinyspawn","vendor-chunks/is-unix","vendor-chunks/dargs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-info%2Froute&page=%2Fapi%2Fvideo-info%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-info%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();