/**
 * http://en.wikipedia.org/wiki/YouTube#Quality_and_formats
 */
module.exports = {
  5: {
    mimeType: 'video/flv; codecs="<PERSON><PERSON>son H.283, mp3"',
    qualityLabel: "240p",
    bitrate: 250000,
    audioBitrate: 64,
  },

  6: {
    mimeType: 'video/flv; codecs="Sorenson H.263, mp3"',
    qualityLabel: "270p",
    bitrate: 800000,
    audioBitrate: 64,
  },

  13: {
    mimeType: 'video/3gp; codecs="MPEG-4 Visual, aac"',
    qualityLabel: null,
    bitrate: 500000,
    audioBitrate: null,
  },

  17: {
    mimeType: 'video/3gp; codecs="MPEG-4 Visual, aac"',
    qualityLabel: "144p",
    bitrate: 50000,
    audioBitrate: 24,
  },

  18: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "360p",
    bitrate: 500000,
    audioBitrate: 96,
  },

  22: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 2000000,
    audioBitrate: 192,
  },

  34: {
    mimeType: 'video/flv; codecs="H.264, aac"',
    qualityLabel: "360p",
    bitrate: 500000,
    audioBitrate: 128,
  },

  35: {
    mimeType: 'video/flv; codecs="H.264, aac"',
    qualityLabel: "480p",
    bitrate: 800000,
    audioBitrate: 128,
  },

  36: {
    mimeType: 'video/3gp; codecs="MPEG-4 Visual, aac"',
    qualityLabel: "240p",
    bitrate: 175000,
    audioBitrate: 32,
  },

  37: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "1080p",
    bitrate: 3000000,
    audioBitrate: 192,
  },

  38: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "3072p",
    bitrate: 3500000,
    audioBitrate: 192,
  },

  43: {
    mimeType: 'video/webm; codecs="VP8, vorbis"',
    qualityLabel: "360p",
    bitrate: 500000,
    audioBitrate: 128,
  },

  44: {
    mimeType: 'video/webm; codecs="VP8, vorbis"',
    qualityLabel: "480p",
    bitrate: 1000000,
    audioBitrate: 128,
  },

  45: {
    mimeType: 'video/webm; codecs="VP8, vorbis"',
    qualityLabel: "720p",
    bitrate: 2000000,
    audioBitrate: 192,
  },

  46: {
    mimeType: 'audio/webm; codecs="vp8, vorbis"',
    qualityLabel: "1080p",
    bitrate: null,
    audioBitrate: 192,
  },

  82: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "360p",
    bitrate: 500000,
    audioBitrate: 96,
  },

  83: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "240p",
    bitrate: 500000,
    audioBitrate: 96,
  },

  84: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 2000000,
    audioBitrate: 192,
  },

  85: {
    mimeType: 'video/mp4; codecs="H.264, aac"',
    qualityLabel: "1080p",
    bitrate: 3000000,
    audioBitrate: 192,
  },

  91: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "144p",
    bitrate: 100000,
    audioBitrate: 48,
  },

  92: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "240p",
    bitrate: 150000,
    audioBitrate: 48,
  },

  93: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "360p",
    bitrate: 500000,
    audioBitrate: 128,
  },

  94: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "480p",
    bitrate: 800000,
    audioBitrate: 128,
  },

  95: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 1500000,
    audioBitrate: 256,
  },

  96: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "1080p",
    bitrate: 2500000,
    audioBitrate: 256,
  },

  100: {
    mimeType: 'audio/webm; codecs="VP8, vorbis"',
    qualityLabel: "360p",
    bitrate: null,
    audioBitrate: 128,
  },

  101: {
    mimeType: 'audio/webm; codecs="VP8, vorbis"',
    qualityLabel: "360p",
    bitrate: null,
    audioBitrate: 192,
  },

  102: {
    mimeType: 'audio/webm; codecs="VP8, vorbis"',
    qualityLabel: "720p",
    bitrate: null,
    audioBitrate: 192,
  },

  120: {
    mimeType: 'video/flv; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 2000000,
    audioBitrate: 128,
  },

  127: {
    mimeType: 'audio/ts; codecs="aac"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 96,
  },

  128: {
    mimeType: 'audio/ts; codecs="aac"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 96,
  },

  132: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "240p",
    bitrate: 150000,
    audioBitrate: 48,
  },

  133: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "240p",
    bitrate: 200000,
    audioBitrate: null,
  },

  134: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "360p",
    bitrate: 300000,
    audioBitrate: null,
  },

  135: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "480p",
    bitrate: 500000,
    audioBitrate: null,
  },

  136: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "720p",
    bitrate: 1000000,
    audioBitrate: null,
  },

  137: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "1080p",
    bitrate: 2500000,
    audioBitrate: null,
  },

  138: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "4320p",
    bitrate: 13500000,
    audioBitrate: null,
  },

  139: {
    mimeType: 'audio/mp4; codecs="aac"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 48,
  },

  140: {
    mimeType: 'audio/m4a; codecs="aac"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 128,
  },

  141: {
    mimeType: 'audio/mp4; codecs="aac"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 256,
  },

  151: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 50000,
    audioBitrate: 24,
  },

  160: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "144p",
    bitrate: 100000,
    audioBitrate: null,
  },

  171: {
    mimeType: 'audio/webm; codecs="vorbis"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 128,
  },

  172: {
    mimeType: 'audio/webm; codecs="vorbis"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 192,
  },

  231: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "480p",
    bitrate: 500000,
    audioBitrate: null,
  },

  232: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 800000,
    audioBitrate: null,
  },

  242: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "240p",
    bitrate: 100000,
    audioBitrate: null,
  },

  243: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "360p",
    bitrate: 250000,
    audioBitrate: null,
  },

  244: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "480p",
    bitrate: 500000,
    audioBitrate: null,
  },

  247: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "720p",
    bitrate: 700000,
    audioBitrate: null,
  },

  248: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1080p",
    bitrate: 1500000,
    audioBitrate: null,
  },

  249: {
    mimeType: 'audio/webm; codecs="opus"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 48,
  },

  250: {
    mimeType: 'audio/webm; codecs="opus"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 64,
  },

  251: {
    mimeType: 'audio/webm; codecs="opus"',
    qualityLabel: null,
    bitrate: null,
    audioBitrate: 160,
  },

  264: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "1440p",
    bitrate: 4000000,
    audioBitrate: null,
  },

  266: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "2160p",
    bitrate: 12500000,
    audioBitrate: null,
  },

  270: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "1080p",
    bitrate: 2500000,
    audioBitrate: null,
  },

  271: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1440p",
    bitrate: 9000000,
    audioBitrate: null,
  },

  272: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "4320p",
    bitrate: 20000000,
    audioBitrate: null,
  },

  278: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "144p 30fps",
    bitrate: 80000,
    audioBitrate: null,
  },

  298: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "720p",
    bitrate: 3000000,
    audioBitrate: null,
  },

  299: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "1080p",
    bitrate: 5500000,
    audioBitrate: null,
  },

  300: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "720p",
    bitrate: 1318000,
    audioBitrate: 48,
  },

  301: {
    mimeType: 'video/ts; codecs="H.264, aac"',
    qualityLabel: "1080p",
    bitrate: 3000000,
    audioBitrate: 128,
  },

  302: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "720p HFR",
    bitrate: 2500000,
    audioBitrate: null,
  },

  303: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1080p HFR",
    bitrate: 5000000,
    audioBitrate: null,
  },

  308: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1440p HFR",
    bitrate: 10000000,
    audioBitrate: null,
  },

  311: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "720p",
    bitrate: 1250000,
    audioBitrate: null,
  },

  312: {
    mimeType: 'video/mp4; codecs="H.264"',
    qualityLabel: "1080p",
    bitrate: 2500000,
    audioBitrate: null,
  },

  313: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "2160p",
    bitrate: 13000000,
    audioBitrate: null,
  },

  315: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "2160p HFR",
    bitrate: 20000000,
    audioBitrate: null,
  },

  330: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "144p HDR, HFR",
    bitrate: 80000,
    audioBitrate: null,
  },

  331: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "240p HDR, HFR",
    bitrate: 100000,
    audioBitrate: null,
  },

  332: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "360p HDR, HFR",
    bitrate: 250000,
    audioBitrate: null,
  },

  333: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "240p HDR, HFR",
    bitrate: 500000,
    audioBitrate: null,
  },

  334: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "720p HDR, HFR",
    bitrate: 1000000,
    audioBitrate: null,
  },

  335: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1080p HDR, HFR",
    bitrate: 1500000,
    audioBitrate: null,
  },

  336: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "1440p HDR, HFR",
    bitrate: 5000000,
    audioBitrate: null,
  },

  337: {
    mimeType: 'video/webm; codecs="VP9"',
    qualityLabel: "2160p HDR, HFR",
    bitrate: 12000000,
    audioBitrate: null,
  },
};
