/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/download/route";
exports.ids = ["app/api/download/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdownload%2Froute&page=%2Fapi%2Fdownload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdownload%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdownload%2Froute&page=%2Fapi%2Fdownload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdownload%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_YouTube_Downloader_youtube_downloader_src_app_api_download_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/download/route.ts */ \"(rsc)/./src/app/api/download/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/download/route\",\n        pathname: \"/api/download\",\n        filename: \"route\",\n        bundlePath: \"app/api/download/route\"\n    },\n    resolvedPagePath: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\api\\\\download\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_YouTube_Downloader_youtube_downloader_src_app_api_download_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdownload%2Froute&page=%2Fapi%2Fdownload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdownload%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/download/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/download/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\nasync function POST(request) {\n    try {\n        const { url, format, quality } = await request.json();\n        if (!url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"URL is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Basic YouTube URL validation\n        const youtubeRegex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/)[\\w-]+/;\n        if (!youtubeRegex.test(url)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid YouTube URL\"\n            }, {\n                status: 400\n            });\n        }\n        // Get video info first to get the title\n        const infoCommand = `yt-dlp --dump-single-json --no-check-certificates --no-warnings \"${url}\"`;\n        const { stdout } = await execAsync(infoCommand);\n        const info = JSON.parse(stdout);\n        const title = (info.title || \"video\").replace(/[^\\w\\s-]/g, \"\").trim();\n        if (format === \"mp3\") {\n            // Download audio only and convert to MP3\n            const audioCommand = `yt-dlp --extract-audio --audio-format mp3 --audio-quality 0 --no-check-certificates --no-warnings -o - \"${url}\"`;\n            return new Promise((resolve, reject)=>{\n                const child = (0,child_process__WEBPACK_IMPORTED_MODULE_1__.exec)(audioCommand, {\n                    encoding: \"buffer\",\n                    maxBuffer: 1024 * 1024 * 100\n                }); // 100MB buffer\n                const chunks = [];\n                child.stdout?.on(\"data\", (chunk)=>{\n                    chunks.push(chunk);\n                });\n                child.on(\"close\", (code)=>{\n                    if (code === 0) {\n                        const buffer = Buffer.concat(chunks);\n                        const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n                            headers: {\n                                \"Content-Type\": \"audio/mpeg\",\n                                \"Content-Disposition\": `attachment; filename=\"${title}.mp3\"`\n                            }\n                        });\n                        resolve(response);\n                    } else {\n                        reject(next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: \"Audio download failed\"\n                        }, {\n                            status: 500\n                        }));\n                    }\n                });\n                child.on(\"error\", (err)=>{\n                    console.error(\"Audio download error:\", err);\n                    reject(next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Audio download failed\"\n                    }, {\n                        status: 500\n                    }));\n                });\n            });\n        } else {\n            // Download video with audio\n            let formatSelector;\n            if (quality) {\n                const height = quality.replace(\"p\", \"\");\n                if (parseInt(height) <= 360) {\n                    // For 360p and below, use format 18 which has both video and audio\n                    formatSelector = \"18/best[height<=360]\";\n                } else {\n                    // For higher qualities, be more specific about format selection\n                    switch(height){\n                        case \"1080\":\n                            formatSelector = \"137+140/best[height<=1080]+bestaudio/best[height<=1080]\";\n                            break;\n                        case \"720\":\n                            formatSelector = \"136+140/best[height<=720]+bestaudio/best[height<=720]\";\n                            break;\n                        case \"480\":\n                            formatSelector = \"135+140/best[height<=480]+bestaudio/best[height<=480]\";\n                            break;\n                        default:\n                            formatSelector = `best[height<=${height}]+bestaudio/best[height<=${height}]`;\n                    }\n                }\n            } else {\n                // Default: best quality with audio\n                formatSelector = \"best+bestaudio/best\";\n            }\n            const videoCommand = `yt-dlp -f \"${formatSelector}\" --merge-output-format mp4 --no-check-certificates --no-warnings -o - \"${url}\"`;\n            return new Promise((resolve, reject)=>{\n                const child = (0,child_process__WEBPACK_IMPORTED_MODULE_1__.exec)(videoCommand, {\n                    encoding: \"buffer\",\n                    maxBuffer: 1024 * 1024 * 200\n                }); // 200MB buffer\n                const chunks = [];\n                child.stdout?.on(\"data\", (chunk)=>{\n                    chunks.push(chunk);\n                });\n                child.on(\"close\", (code)=>{\n                    if (code === 0) {\n                        const buffer = Buffer.concat(chunks);\n                        const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n                            headers: {\n                                \"Content-Type\": \"video/mp4\",\n                                \"Content-Disposition\": `attachment; filename=\"${title}.mp4\"`\n                            }\n                        });\n                        resolve(response);\n                    } else {\n                        reject(next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: \"Video download failed\"\n                        }, {\n                            status: 500\n                        }));\n                    }\n                });\n                child.on(\"error\", (err)=>{\n                    console.error(\"Video download error:\", err);\n                    reject(next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Video download failed\"\n                    }, {\n                        status: 500\n                    }));\n                });\n            });\n        }\n    } catch (error) {\n        console.error(\"Error downloading:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Download failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/download/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdownload%2Froute&page=%2Fapi%2Fdownload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdownload%2Froute.ts&appDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CYouTube%20Downloader%5Cyoutube-downloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();