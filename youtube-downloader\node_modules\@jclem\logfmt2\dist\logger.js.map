{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../lib/logger.ts"], "names": [], "mappings": ";;;AACA,qCAA+B;AAS/B;;GAEG;AACH,MAAa,MAAM;IACT,OAAO,CAAY;IACnB,MAAM,GAAW,EAAE,CAAA;IACV,MAAM,CAAuB;IAE9C,YAAY,EAAC,MAAM,EAAE,OAAO,KAAgB,EAAE;QAC5C,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,MAAM,CAAA;QACtC,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;IAC9B,CAAC;IAED,MAAM,CAAC,GAAG,CACR,IAAgB,EAChB,OAAyC,EAAE;QAE3C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAA;QAC5C,MAAM,WAAW,GAAG,IAAA,eAAM,EAAC,IAAI,CAAC,CAAA;QAChC,MAAM,CAAC,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAsB;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;IAC9C,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,OAAmB,EAAE;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QACvC,MAAM,WAAW,GAAG,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;QAC9D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,CAAA;IACvC,CAAC;IAED;;;;;;;;OAQG;IACH,QAAQ,CAAC,KAAY,EAAE,OAAmB,EAAE;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAC,CAAC,CAAA;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QACrC,MAAM,YAAY,GAAG,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAA;QACxC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAE7D,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAC1C,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAC,SAAS,EAAC,CAAC,CAAA;QAC9C,CAAC,CAAC,CAAA;QAEF,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,CAAA;QACzC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,KAAa;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IACjC,CAAC;IAEO,cAAc;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAW,EAAE,CAAA;QAEzB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;SACrC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,KAAK,CAAC,GAAG,IAAkB;QACjC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;IACnC,CAAC;IAEO,YAAY,CAAC,GAAG,IAAkB;QACxC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA;IACjD,CAAC;IAEO,cAAc;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAA;IAC/C,CAAC;CACF;AA/FD,wBA+FC"}