"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tinyspawn";
exports.ids = ["vendor-chunks/tinyspawn"];
exports.modules = {

/***/ "(rsc)/./node_modules/tinyspawn/src/index.js":
/*!*********************************************!*\
  !*** ./node_modules/tinyspawn/src/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { spawn } = __webpack_require__(/*! child_process */ \"child_process\")\nconst { EOL } = __webpack_require__(/*! os */ \"os\")\n\nconst EE_PROPS = Object.getOwnPropertyNames((__webpack_require__(/*! events */ \"events\").EventEmitter).prototype)\n  .filter(name => !name.startsWith('_'))\n  .concat(['kill', 'ref', 'unref'])\n\nconst eos = (stream, listener, buffer = []) =>\n  stream[listener] ? stream[listener].on('data', data => buffer.push(data)) && buffer : buffer\n\nconst createChildProcessError = ({ cmd, cmdArgs, childProcess }) => {\n  const command = `${cmd} ${cmdArgs.join(' ')}`\n  let message = `The command spawned as:${EOL}${EOL}`\n  message += `  \\`${command}\\`${EOL}${EOL}`\n  message += `exited with:${EOL}${EOL}`\n  message += `  \\`{ signal: '${childProcess.signalCode}', code: ${childProcess.exitCode} }\\` ${EOL}${EOL}`\n  message += `with the following trace:${EOL}`\n  const error = new Error(message)\n  error.command = command\n  error.name = 'ChildProcessError'\n\n  Object.keys(childProcess)\n    .filter(key => !key.startsWith('_') && !['stdio', 'stdin'].includes(key))\n    .forEach(key => {\n      error[key] = childProcess[key]\n    })\n\n  return error\n}\n\nconst clean = str => str.trim().replace(/\\n$/, '')\n\nconst parse =\n  (buffer, { json } = {}) =>\n    (encoding, start, end) => {\n      const data = clean(Buffer.concat(buffer).toString(encoding, start, end))\n      return json ? JSON.parse(data) : data\n    }\n\nconst extend = defaults => (input, args, options) => {\n  if (!(args instanceof Array)) {\n    options = args\n    args = []\n  }\n  const [cmd, ...cmdArgs] = input.split(' ').concat(args).filter(Boolean)\n  let childProcess\n\n  const promise = new Promise((resolve, reject) => {\n    const opts = { ...defaults, ...options }\n    childProcess = spawn(cmd, cmdArgs, opts)\n    const stdout = eos(childProcess, 'stdout')\n    const stderr = eos(childProcess, 'stderr')\n\n    childProcess.on('error', reject).on('exit', exitCode => {\n      Object.defineProperty(childProcess, 'stdout', {\n        get: parse(stdout, opts)\n      })\n      Object.defineProperty(childProcess, 'stderr', { get: parse(stderr) })\n      if (exitCode !== 0) {\n        const error = createChildProcessError({ cmd, cmdArgs, childProcess })\n        if (opts.reject !== false) return reject(error)\n        childProcess.error = error\n      }\n      return resolve(childProcess)\n    })\n  })\n\n  const subprocess = Object.assign(promise, childProcess)\n  if (childProcess) {\n    EE_PROPS.forEach(name => (subprocess[name] = childProcess[name].bind(childProcess)))\n  }\n  return subprocess\n}\n\nconst $ = extend()\n$.extend = extend\n$.json = $.extend({ json: true })\n\nmodule.exports = $\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tinyspawn/src/index.js\n");

/***/ })

};
;