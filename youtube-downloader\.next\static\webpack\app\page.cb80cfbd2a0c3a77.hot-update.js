"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Loader2,Music,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    _s();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoInfo, setVideoInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [downloading, setDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetInfo = async ()=>{\n        if (!url) return;\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/video-info\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    url\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get video info\");\n            }\n            setVideoInfo(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDownload = async (format, quality)=>{\n        if (!url) return;\n        setDownloading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/download\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    url,\n                    format,\n                    quality\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || \"Download failed\");\n            }\n            // Create download link\n            const blob = await response.blob();\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = downloadUrl;\n            const extension = format === \"mp3\" ? \"webm\" : \"mp4\";\n            a.download = \"\".concat((videoInfo === null || videoInfo === void 0 ? void 0 : videoInfo.title) || \"video\", \".\").concat(extension);\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(downloadUrl);\n            document.body.removeChild(a);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Download failed\");\n        } finally{\n            setDownloading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"YouTube Downloader\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                children: \"Download high-quality YouTube videos and audio files\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Demo Version:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" This is a demonstration of the UI. Downloads will generate demo files. For production use, implement with yt-dlp or similar tools.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    placeholder: \"Paste YouTube URL here...\",\n                                    className: \"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGetInfo,\n                                    disabled: loading || !url,\n                                    className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                    children: [\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Info\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800 dark:text-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this),\n                    videoInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: videoInfo.thumbnail,\n                                    alt: videoInfo.title,\n                                    className: \"w-full md:w-80 h-48 object-cover rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: videoInfo.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                            children: [\n                                                \"Duration: \",\n                                                videoInfo.duration\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                    children: \"Download Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                                children: \"Audio (WebM)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 175,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                                                children: \"High quality audio only - can be converted to MP3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 178,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDownload(\"mp3\"),\n                                                                disabled: downloading,\n                                                                className: \"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                                children: [\n                                                                    downloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        className: \"w-4 h-4 animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Download\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this),\n                                                videoInfo.formats.map((format, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-6 h-6 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                                    children: [\n                                                                                        format.quality,\n                                                                                        \" (\",\n                                                                                        format.format,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 208,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                                                    children: format.hasAudio ? \"Video + Audio\" : \"Video only\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 211,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDownload(\"mp4\", format.quality),\n                                                                    disabled: downloading,\n                                                                    className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                                    children: [\n                                                                        downloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            className: \"w-4 h-4 animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Loader2_Music_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"Download\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\YouTube Downloader\\\\youtube-downloader\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Qucz7aNz34KUsbAGh5/X91VMI1c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});