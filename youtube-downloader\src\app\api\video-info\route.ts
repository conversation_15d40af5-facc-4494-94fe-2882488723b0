import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // For demo purposes, return mock data
    // In a production environment, you would use yt-dlp or a working YouTube API
    const videoInfo = {
      title: "Sample YouTube Video",
      duration: "3:45",
      thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
      formats: [
        {
          quality: "1080p",
          format: "mp4",
          hasAudio: true,
        },
        {
          quality: "720p",
          format: "mp4",
          hasAudio: true,
        },
        {
          quality: "480p",
          format: "mp4",
          hasAudio: true,
        },
        {
          quality: "360p",
          format: "mp4",
          hasAudio: true,
        },
      ],
    };

    return NextResponse.json(videoInfo);
  } catch (error) {
    console.error("Error getting video info:", error);
    return NextResponse.json(
      { error: "Failed to get video information" },
      { status: 500 }
    );
  }
}
