import { NextRequest, NextResponse } from "next/server";
import youtubedl from "youtube-dl-exec";

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // Get video info using yt-dlp
    const info = await youtubedl(
      url,
      {
        dumpSingleJson: true,
        noCheckCertificates: true,
        noWarnings: true,
        preferFreeFormats: true,
        addHeader: ["referer:youtube.com", "user-agent:googlebot"],
      },
      {
        binaryPath: "yt-dlp",
      }
    );

    // Extract available formats
    const formats =
      info.formats
        ?.filter(
          (format: any) => format.vcodec !== "none" && format.ext === "mp4"
        )
        .map((format: any) => ({
          quality: format.height
            ? `${format.height}p`
            : format.format_note || "Unknown",
          format: format.ext || "mp4",
          hasAudio: format.acodec !== "none",
          format_id: format.format_id,
        }))
        .filter(
          (format: any, index: number, self: any[]) =>
            index === self.findIndex((f: any) => f.quality === format.quality)
        )
        .sort((a: any, b: any) => {
          const aHeight = parseInt(a.quality) || 0;
          const bHeight = parseInt(b.quality) || 0;
          return bHeight - aHeight;
        }) || [];

    // Format duration
    const duration = info.duration
      ? `${Math.floor(info.duration / 60)}:${(info.duration % 60)
          .toString()
          .padStart(2, "0")}`
      : "Unknown";

    const videoInfo = {
      title: info.title || "Unknown Title",
      duration,
      thumbnail: info.thumbnail || "",
      formats,
    };

    return NextResponse.json(videoInfo);
  } catch (error) {
    console.error("Error getting video info:", error);
    return NextResponse.json(
      {
        error:
          "Failed to get video information. Please check the URL and try again.",
      },
      { status: 500 }
    );
  }
}
