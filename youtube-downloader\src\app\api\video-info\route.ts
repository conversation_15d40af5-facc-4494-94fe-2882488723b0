import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Basic YouTube URL validation
    const youtubeRegex =
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(url)) {
      return NextResponse.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    // Get video info using yt-dlp command directly
    const command = `yt-dlp --dump-single-json --no-check-certificates --no-warnings "${url}"`;
    const { stdout } = await execAsync(command);
    const info = JSON.parse(stdout);

    // Extract available formats - focus on common video qualities
    const commonQualities = [
      {
        quality: "1080p",
        hasAudio: false,
        note: "High quality (video + audio merged)",
      },
      {
        quality: "720p",
        hasAudio: false,
        note: "HD quality (video + audio merged)",
      },
      {
        quality: "480p",
        hasAudio: false,
        note: "Standard quality (video + audio merged)",
      },
      { quality: "360p", hasAudio: true, note: "Good quality with audio" },
      {
        quality: "240p",
        hasAudio: false,
        note: "Low quality (video + audio merged)",
      },
    ];

    // Check which qualities are actually available
    const availableFormats = info.formats || [];
    const formats = commonQualities
      .filter((quality) => {
        const height = parseInt(quality.quality);
        return availableFormats.some(
          (format: any) =>
            format.height && format.height >= height && format.vcodec !== "none"
        );
      })
      .map((quality) => ({
        quality: quality.quality,
        format: "mp4",
        hasAudio: quality.quality === "360p", // Only 360p has direct audio, others will be merged
      }));

    // Format duration
    const duration = info.duration
      ? `${Math.floor(info.duration / 60)}:${(info.duration % 60)
          .toString()
          .padStart(2, "0")}`
      : "Unknown";

    const videoInfo = {
      title: info.title || "Unknown Title",
      duration,
      thumbnail: info.thumbnail || "",
      formats,
    };

    return NextResponse.json(videoInfo);
  } catch (error) {
    console.error("Error getting video info:", error);
    return NextResponse.json(
      {
        error:
          "Failed to get video information. Please check the URL and try again.",
      },
      { status: 500 }
    );
  }
}
