/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ytdl-core";
exports.ids = ["vendor-chunks/ytdl-core"];
exports.modules = {

/***/ "(rsc)/./node_modules/ytdl-core/lib/cache.js":
/*!*********************************************!*\
  !*** ./node_modules/ytdl-core/lib/cache.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { setTimeout } = __webpack_require__(/*! timers */ \"timers\");\n\n// A cache that expires.\nmodule.exports = class Cache extends Map {\n  constructor(timeout = 1000) {\n    super();\n    this.timeout = timeout;\n  }\n  set(key, value) {\n    if (this.has(key)) {\n      clearTimeout(super.get(key).tid);\n    }\n    super.set(key, {\n      tid: setTimeout(this.delete.bind(this, key), this.timeout).unref(),\n      value,\n    });\n  }\n  get(key) {\n    let entry = super.get(key);\n    if (entry) {\n      return entry.value;\n    }\n    return null;\n  }\n  getOrSet(key, fn) {\n    if (this.has(key)) {\n      return this.get(key);\n    } else {\n      let value = fn();\n      this.set(key, value);\n      (async() => {\n        try {\n          await value;\n        } catch (err) {\n          this.delete(key);\n        }\n      })();\n      return value;\n    }\n  }\n  delete(key) {\n    let entry = super.get(key);\n    if (entry) {\n      clearTimeout(entry.tid);\n      super.delete(key);\n    }\n  }\n  clear() {\n    for (let entry of this.values()) {\n      clearTimeout(entry.tid);\n    }\n    super.clear();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/format-utils.js":
/*!****************************************************!*\
  !*** ./node_modules/ytdl-core/lib/format-utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/ytdl-core/lib/utils.js\");\nconst FORMATS = __webpack_require__(/*! ./formats */ \"(rsc)/./node_modules/ytdl-core/lib/formats.js\");\n\n\n// Use these to help sort formats, higher index is better.\nconst audioEncodingRanks = [\n  'mp4a',\n  'mp3',\n  'vorbis',\n  'aac',\n  'opus',\n  'flac',\n];\nconst videoEncodingRanks = [\n  'mp4v',\n  'avc1',\n  'Sorenson H.283',\n  'MPEG-4 Visual',\n  'VP8',\n  'VP9',\n  'H.264',\n];\n\nconst getVideoBitrate = format => format.bitrate || 0;\nconst getVideoEncodingRank = format =>\n  videoEncodingRanks.findIndex(enc => format.codecs && format.codecs.includes(enc));\nconst getAudioBitrate = format => format.audioBitrate || 0;\nconst getAudioEncodingRank = format =>\n  audioEncodingRanks.findIndex(enc => format.codecs && format.codecs.includes(enc));\n\n\n/**\n * Sort formats by a list of functions.\n *\n * @param {Object} a\n * @param {Object} b\n * @param {Array.<Function>} sortBy\n * @returns {number}\n */\nconst sortFormatsBy = (a, b, sortBy) => {\n  let res = 0;\n  for (let fn of sortBy) {\n    res = fn(b) - fn(a);\n    if (res !== 0) {\n      break;\n    }\n  }\n  return res;\n};\n\n\nconst sortFormatsByVideo = (a, b) => sortFormatsBy(a, b, [\n  format => parseInt(format.qualityLabel),\n  getVideoBitrate,\n  getVideoEncodingRank,\n]);\n\n\nconst sortFormatsByAudio = (a, b) => sortFormatsBy(a, b, [\n  getAudioBitrate,\n  getAudioEncodingRank,\n]);\n\n\n/**\n * Sort formats from highest quality to lowest.\n *\n * @param {Object} a\n * @param {Object} b\n * @returns {number}\n */\nexports.sortFormats = (a, b) => sortFormatsBy(a, b, [\n  // Formats with both video and audio are ranked highest.\n  format => +!!format.isHLS,\n  format => +!!format.isDashMPD,\n  format => +(format.contentLength > 0),\n  format => +(format.hasVideo && format.hasAudio),\n  format => +format.hasVideo,\n  format => parseInt(format.qualityLabel) || 0,\n  getVideoBitrate,\n  getAudioBitrate,\n  getVideoEncodingRank,\n  getAudioEncodingRank,\n]);\n\n\n/**\n * Choose a format depending on the given options.\n *\n * @param {Array.<Object>} formats\n * @param {Object} options\n * @returns {Object}\n * @throws {Error} when no format matches the filter/format rules\n */\nexports.chooseFormat = (formats, options) => {\n  if (typeof options.format === 'object') {\n    if (!options.format.url) {\n      throw Error('Invalid format given, did you use `ytdl.getInfo()`?');\n    }\n    return options.format;\n  }\n\n  if (options.filter) {\n    formats = exports.filterFormats(formats, options.filter);\n  }\n\n  // We currently only support HLS-Formats for livestreams\n  // So we (now) remove all non-HLS streams\n  if (formats.some(fmt => fmt.isHLS)) {\n    formats = formats.filter(fmt => fmt.isHLS || !fmt.isLive);\n  }\n\n  let format;\n  const quality = options.quality || 'highest';\n  switch (quality) {\n    case 'highest':\n      format = formats[0];\n      break;\n\n    case 'lowest':\n      format = formats[formats.length - 1];\n      break;\n\n    case 'highestaudio': {\n      formats = exports.filterFormats(formats, 'audio');\n      formats.sort(sortFormatsByAudio);\n      // Filter for only the best audio format\n      const bestAudioFormat = formats[0];\n      formats = formats.filter(f => sortFormatsByAudio(bestAudioFormat, f) === 0);\n      // Check for the worst video quality for the best audio quality and pick according\n      // This does not loose default sorting of video encoding and bitrate\n      const worstVideoQuality = formats.map(f => parseInt(f.qualityLabel) || 0).sort((a, b) => a - b)[0];\n      format = formats.find(f => (parseInt(f.qualityLabel) || 0) === worstVideoQuality);\n      break;\n    }\n\n    case 'lowestaudio':\n      formats = exports.filterFormats(formats, 'audio');\n      formats.sort(sortFormatsByAudio);\n      format = formats[formats.length - 1];\n      break;\n\n    case 'highestvideo': {\n      formats = exports.filterFormats(formats, 'video');\n      formats.sort(sortFormatsByVideo);\n      // Filter for only the best video format\n      const bestVideoFormat = formats[0];\n      formats = formats.filter(f => sortFormatsByVideo(bestVideoFormat, f) === 0);\n      // Check for the worst audio quality for the best video quality and pick according\n      // This does not loose default sorting of audio encoding and bitrate\n      const worstAudioQuality = formats.map(f => f.audioBitrate || 0).sort((a, b) => a - b)[0];\n      format = formats.find(f => (f.audioBitrate || 0) === worstAudioQuality);\n      break;\n    }\n\n    case 'lowestvideo':\n      formats = exports.filterFormats(formats, 'video');\n      formats.sort(sortFormatsByVideo);\n      format = formats[formats.length - 1];\n      break;\n\n    default:\n      format = getFormatByQuality(quality, formats);\n      break;\n  }\n\n  if (!format) {\n    throw Error(`No such format found: ${quality}`);\n  }\n  return format;\n};\n\n/**\n * Gets a format based on quality or array of quality's\n *\n * @param {string|[string]} quality\n * @param {[Object]} formats\n * @returns {Object}\n */\nconst getFormatByQuality = (quality, formats) => {\n  let getFormat = itag => formats.find(format => `${format.itag}` === `${itag}`);\n  if (Array.isArray(quality)) {\n    return getFormat(quality.find(q => getFormat(q)));\n  } else {\n    return getFormat(quality);\n  }\n};\n\n\n/**\n * @param {Array.<Object>} formats\n * @param {Function} filter\n * @returns {Array.<Object>}\n */\nexports.filterFormats = (formats, filter) => {\n  let fn;\n  switch (filter) {\n    case 'videoandaudio':\n    case 'audioandvideo':\n      fn = format => format.hasVideo && format.hasAudio;\n      break;\n\n    case 'video':\n      fn = format => format.hasVideo;\n      break;\n\n    case 'videoonly':\n      fn = format => format.hasVideo && !format.hasAudio;\n      break;\n\n    case 'audio':\n      fn = format => format.hasAudio;\n      break;\n\n    case 'audioonly':\n      fn = format => !format.hasVideo && format.hasAudio;\n      break;\n\n    default:\n      if (typeof filter === 'function') {\n        fn = filter;\n      } else {\n        throw TypeError(`Given filter (${filter}) is not supported`);\n      }\n  }\n  return formats.filter(format => !!format.url && fn(format));\n};\n\n\n/**\n * @param {Object} format\n * @returns {Object}\n */\nexports.addFormatMeta = format => {\n  format = Object.assign({}, FORMATS[format.itag], format);\n  format.hasVideo = !!format.qualityLabel;\n  format.hasAudio = !!format.audioBitrate;\n  format.container = format.mimeType ?\n    format.mimeType.split(';')[0].split('/')[1] : null;\n  format.codecs = format.mimeType ?\n    utils.between(format.mimeType, 'codecs=\"', '\"') : null;\n  format.videoCodec = format.hasVideo && format.codecs ?\n    format.codecs.split(', ')[0] : null;\n  format.audioCodec = format.hasAudio && format.codecs ?\n    format.codecs.split(', ').slice(-1)[0] : null;\n  format.isLive = /\\bsource[/=]yt_live_broadcast\\b/.test(format.url);\n  format.isHLS = /\\/manifest\\/hls_(variant|playlist)\\//.test(format.url);\n  format.isDashMPD = /\\/manifest\\/dash\\//.test(format.url);\n  return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/format-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/formats.js":
/*!***********************************************!*\
  !*** ./node_modules/ytdl-core/lib/formats.js ***!
  \***********************************************/
/***/ ((module) => {

eval("/**\n * http://en.wikipedia.org/wiki/YouTube#Quality_and_formats\n */\nmodule.exports = {\n\n  5: {\n    mimeType: 'video/flv; codecs=\"Sorenson H.283, mp3\"',\n    qualityLabel: '240p',\n    bitrate: 250000,\n    audioBitrate: 64,\n  },\n\n  6: {\n    mimeType: 'video/flv; codecs=\"Sorenson H.263, mp3\"',\n    qualityLabel: '270p',\n    bitrate: 800000,\n    audioBitrate: 64,\n  },\n\n  13: {\n    mimeType: 'video/3gp; codecs=\"MPEG-4 Visual, aac\"',\n    qualityLabel: null,\n    bitrate: 500000,\n    audioBitrate: null,\n  },\n\n  17: {\n    mimeType: 'video/3gp; codecs=\"MPEG-4 Visual, aac\"',\n    qualityLabel: '144p',\n    bitrate: 50000,\n    audioBitrate: 24,\n  },\n\n  18: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '360p',\n    bitrate: 500000,\n    audioBitrate: 96,\n  },\n\n  22: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 2000000,\n    audioBitrate: 192,\n  },\n\n  34: {\n    mimeType: 'video/flv; codecs=\"H.264, aac\"',\n    qualityLabel: '360p',\n    bitrate: 500000,\n    audioBitrate: 128,\n  },\n\n  35: {\n    mimeType: 'video/flv; codecs=\"H.264, aac\"',\n    qualityLabel: '480p',\n    bitrate: 800000,\n    audioBitrate: 128,\n  },\n\n  36: {\n    mimeType: 'video/3gp; codecs=\"MPEG-4 Visual, aac\"',\n    qualityLabel: '240p',\n    bitrate: 175000,\n    audioBitrate: 32,\n  },\n\n  37: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '1080p',\n    bitrate: 3000000,\n    audioBitrate: 192,\n  },\n\n  38: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '3072p',\n    bitrate: 3500000,\n    audioBitrate: 192,\n  },\n\n  43: {\n    mimeType: 'video/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '360p',\n    bitrate: 500000,\n    audioBitrate: 128,\n  },\n\n  44: {\n    mimeType: 'video/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '480p',\n    bitrate: 1000000,\n    audioBitrate: 128,\n  },\n\n  45: {\n    mimeType: 'video/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '720p',\n    bitrate: 2000000,\n    audioBitrate: 192,\n  },\n\n  46: {\n    mimeType: 'audio/webm; codecs=\"vp8, vorbis\"',\n    qualityLabel: '1080p',\n    bitrate: null,\n    audioBitrate: 192,\n  },\n\n  82: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '360p',\n    bitrate: 500000,\n    audioBitrate: 96,\n  },\n\n  83: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '240p',\n    bitrate: 500000,\n    audioBitrate: 96,\n  },\n\n  84: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 2000000,\n    audioBitrate: 192,\n  },\n\n  85: {\n    mimeType: 'video/mp4; codecs=\"H.264, aac\"',\n    qualityLabel: '1080p',\n    bitrate: 3000000,\n    audioBitrate: 192,\n  },\n\n  91: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '144p',\n    bitrate: 100000,\n    audioBitrate: 48,\n  },\n\n  92: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '240p',\n    bitrate: 150000,\n    audioBitrate: 48,\n  },\n\n  93: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '360p',\n    bitrate: 500000,\n    audioBitrate: 128,\n  },\n\n  94: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '480p',\n    bitrate: 800000,\n    audioBitrate: 128,\n  },\n\n  95: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 1500000,\n    audioBitrate: 256,\n  },\n\n  96: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '1080p',\n    bitrate: 2500000,\n    audioBitrate: 256,\n  },\n\n  100: {\n    mimeType: 'audio/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '360p',\n    bitrate: null,\n    audioBitrate: 128,\n  },\n\n  101: {\n    mimeType: 'audio/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '360p',\n    bitrate: null,\n    audioBitrate: 192,\n  },\n\n  102: {\n    mimeType: 'audio/webm; codecs=\"VP8, vorbis\"',\n    qualityLabel: '720p',\n    bitrate: null,\n    audioBitrate: 192,\n  },\n\n  120: {\n    mimeType: 'video/flv; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 2000000,\n    audioBitrate: 128,\n  },\n\n  127: {\n    mimeType: 'audio/ts; codecs=\"aac\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 96,\n  },\n\n  128: {\n    mimeType: 'audio/ts; codecs=\"aac\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 96,\n  },\n\n  132: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '240p',\n    bitrate: 150000,\n    audioBitrate: 48,\n  },\n\n  133: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '240p',\n    bitrate: 200000,\n    audioBitrate: null,\n  },\n\n  134: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '360p',\n    bitrate: 300000,\n    audioBitrate: null,\n  },\n\n  135: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '480p',\n    bitrate: 500000,\n    audioBitrate: null,\n  },\n\n  136: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '720p',\n    bitrate: 1000000,\n    audioBitrate: null,\n  },\n\n  137: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '1080p',\n    bitrate: 2500000,\n    audioBitrate: null,\n  },\n\n  138: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '4320p',\n    bitrate: 13500000,\n    audioBitrate: null,\n  },\n\n  139: {\n    mimeType: 'audio/mp4; codecs=\"aac\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 48,\n  },\n\n  140: {\n    mimeType: 'audio/m4a; codecs=\"aac\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 128,\n  },\n\n  141: {\n    mimeType: 'audio/mp4; codecs=\"aac\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 256,\n  },\n\n  151: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 50000,\n    audioBitrate: 24,\n  },\n\n  160: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '144p',\n    bitrate: 100000,\n    audioBitrate: null,\n  },\n\n  171: {\n    mimeType: 'audio/webm; codecs=\"vorbis\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 128,\n  },\n\n  172: {\n    mimeType: 'audio/webm; codecs=\"vorbis\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 192,\n  },\n\n  242: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '240p',\n    bitrate: 100000,\n    audioBitrate: null,\n  },\n\n  243: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '360p',\n    bitrate: 250000,\n    audioBitrate: null,\n  },\n\n  244: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '480p',\n    bitrate: 500000,\n    audioBitrate: null,\n  },\n\n  247: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '720p',\n    bitrate: 700000,\n    audioBitrate: null,\n  },\n\n  248: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1080p',\n    bitrate: 1500000,\n    audioBitrate: null,\n  },\n\n  249: {\n    mimeType: 'audio/webm; codecs=\"opus\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 48,\n  },\n\n  250: {\n    mimeType: 'audio/webm; codecs=\"opus\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 64,\n  },\n\n  251: {\n    mimeType: 'audio/webm; codecs=\"opus\"',\n    qualityLabel: null,\n    bitrate: null,\n    audioBitrate: 160,\n  },\n\n  264: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '1440p',\n    bitrate: 4000000,\n    audioBitrate: null,\n  },\n\n  266: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '2160p',\n    bitrate: 12500000,\n    audioBitrate: null,\n  },\n\n  271: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1440p',\n    bitrate: 9000000,\n    audioBitrate: null,\n  },\n\n  272: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '4320p',\n    bitrate: 20000000,\n    audioBitrate: null,\n  },\n\n  278: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '144p 30fps',\n    bitrate: 80000,\n    audioBitrate: null,\n  },\n\n  298: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '720p',\n    bitrate: 3000000,\n    audioBitrate: null,\n  },\n\n  299: {\n    mimeType: 'video/mp4; codecs=\"H.264\"',\n    qualityLabel: '1080p',\n    bitrate: 5500000,\n    audioBitrate: null,\n  },\n\n  300: {\n    mimeType: 'video/ts; codecs=\"H.264, aac\"',\n    qualityLabel: '720p',\n    bitrate: 1318000,\n    audioBitrate: 48,\n  },\n\n  302: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '720p HFR',\n    bitrate: 2500000,\n    audioBitrate: null,\n  },\n\n  303: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1080p HFR',\n    bitrate: 5000000,\n    audioBitrate: null,\n  },\n\n  308: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1440p HFR',\n    bitrate: 10000000,\n    audioBitrate: null,\n  },\n\n  313: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '2160p',\n    bitrate: 13000000,\n    audioBitrate: null,\n  },\n\n  315: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '2160p HFR',\n    bitrate: 20000000,\n    audioBitrate: null,\n  },\n\n  330: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '144p HDR, HFR',\n    bitrate: 80000,\n    audioBitrate: null,\n  },\n\n  331: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '240p HDR, HFR',\n    bitrate: 100000,\n    audioBitrate: null,\n  },\n\n  332: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '360p HDR, HFR',\n    bitrate: 250000,\n    audioBitrate: null,\n  },\n\n  333: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '240p HDR, HFR',\n    bitrate: 500000,\n    audioBitrate: null,\n  },\n\n  334: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '720p HDR, HFR',\n    bitrate: 1000000,\n    audioBitrate: null,\n  },\n\n  335: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1080p HDR, HFR',\n    bitrate: 1500000,\n    audioBitrate: null,\n  },\n\n  336: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '1440p HDR, HFR',\n    bitrate: 5000000,\n    audioBitrate: null,\n  },\n\n  337: {\n    mimeType: 'video/webm; codecs=\"VP9\"',\n    qualityLabel: '2160p HDR, HFR',\n    bitrate: 12000000,\n    audioBitrate: null,\n  },\n\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveXRkbC1jb3JlL2xpYi9mb3JtYXRzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSCIsInNvdXJjZXMiOlsiRDpcXFlvdVR1YmUgRG93bmxvYWRlclxceW91dHViZS1kb3dubG9hZGVyXFxub2RlX21vZHVsZXNcXHl0ZGwtY29yZVxcbGliXFxmb3JtYXRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogaHR0cDovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9Zb3VUdWJlI1F1YWxpdHlfYW5kX2Zvcm1hdHNcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cbiAgNToge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vZmx2OyBjb2RlY3M9XCJTb3JlbnNvbiBILjI4MywgbXAzXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzI0MHAnLFxuICAgIGJpdHJhdGU6IDI1MDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IDY0LFxuICB9LFxuXG4gIDY6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL2ZsdjsgY29kZWNzPVwiU29yZW5zb24gSC4yNjMsIG1wM1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyNzBwJyxcbiAgICBiaXRyYXRlOiA4MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiA2NCxcbiAgfSxcblxuICAxMzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vM2dwOyBjb2RlY3M9XCJNUEVHLTQgVmlzdWFsLCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiBudWxsLFxuICAgIGJpdHJhdGU6IDUwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMTc6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvLzNncDsgY29kZWNzPVwiTVBFRy00IFZpc3VhbCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzE0NHAnLFxuICAgIGJpdHJhdGU6IDUwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMjQsXG4gIH0sXG5cbiAgMTg6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICczNjBwJyxcbiAgICBiaXRyYXRlOiA1MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiA5NixcbiAgfSxcblxuICAyMjoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vbXA0OyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzcyMHAnLFxuICAgIGJpdHJhdGU6IDIwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxOTIsXG4gIH0sXG5cbiAgMzQ6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL2ZsdjsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICczNjBwJyxcbiAgICBiaXRyYXRlOiA1MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxMjgsXG4gIH0sXG5cbiAgMzU6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL2ZsdjsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc0ODBwJyxcbiAgICBiaXRyYXRlOiA4MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxMjgsXG4gIH0sXG5cbiAgMzY6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvLzNncDsgY29kZWNzPVwiTVBFRy00IFZpc3VhbCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzI0MHAnLFxuICAgIGJpdHJhdGU6IDE3NTAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IDMyLFxuICB9LFxuXG4gIDM3OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0LCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMTA4MHAnLFxuICAgIGJpdHJhdGU6IDMwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxOTIsXG4gIH0sXG5cbiAgMzg6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICczMDcycCcsXG4gICAgYml0cmF0ZTogMzUwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IDE5MixcbiAgfSxcblxuICA0Mzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA4LCB2b3JiaXNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMzYwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMTI4LFxuICB9LFxuXG4gIDQ0OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDgsIHZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc0ODBwJyxcbiAgICBiaXRyYXRlOiAxMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMTI4LFxuICB9LFxuXG4gIDQ1OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDgsIHZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc3MjBwJyxcbiAgICBiaXRyYXRlOiAyMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMTkyLFxuICB9LFxuXG4gIDQ2OiB7XG4gICAgbWltZVR5cGU6ICdhdWRpby93ZWJtOyBjb2RlY3M9XCJ2cDgsIHZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcxMDgwcCcsXG4gICAgYml0cmF0ZTogbnVsbCxcbiAgICBhdWRpb0JpdHJhdGU6IDE5MixcbiAgfSxcblxuICA4Mjoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vbXA0OyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzM2MHAnLFxuICAgIGJpdHJhdGU6IDUwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IDk2LFxuICB9LFxuXG4gIDgzOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0LCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMjQwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogOTYsXG4gIH0sXG5cbiAgODQ6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc3MjBwJyxcbiAgICBiaXRyYXRlOiAyMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMTkyLFxuICB9LFxuXG4gIDg1OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0LCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMTA4MHAnLFxuICAgIGJpdHJhdGU6IDMwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxOTIsXG4gIH0sXG5cbiAgOTE6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3RzOyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzE0NHAnLFxuICAgIGJpdHJhdGU6IDEwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IDQ4LFxuICB9LFxuXG4gIDkyOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby90czsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyNDBwJyxcbiAgICBiaXRyYXRlOiAxNTAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiA0OCxcbiAgfSxcblxuICA5Mzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vdHM7IGNvZGVjcz1cIkguMjY0LCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMzYwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMTI4LFxuICB9LFxuXG4gIDk0OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby90czsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc0ODBwJyxcbiAgICBiaXRyYXRlOiA4MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxMjgsXG4gIH0sXG5cbiAgOTU6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3RzOyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzcyMHAnLFxuICAgIGJpdHJhdGU6IDE1MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAyNTYsXG4gIH0sXG5cbiAgOTY6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3RzOyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzEwODBwJyxcbiAgICBiaXRyYXRlOiAyNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogMjU2LFxuICB9LFxuXG4gIDEwMDoge1xuICAgIG1pbWVUeXBlOiAnYXVkaW8vd2VibTsgY29kZWNzPVwiVlA4LCB2b3JiaXNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMzYwcCcsXG4gICAgYml0cmF0ZTogbnVsbCxcbiAgICBhdWRpb0JpdHJhdGU6IDEyOCxcbiAgfSxcblxuICAxMDE6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cIlZQOCwgdm9yYmlzXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzM2MHAnLFxuICAgIGJpdHJhdGU6IG51bGwsXG4gICAgYXVkaW9CaXRyYXRlOiAxOTIsXG4gIH0sXG5cbiAgMTAyOiB7XG4gICAgbWltZVR5cGU6ICdhdWRpby93ZWJtOyBjb2RlY3M9XCJWUDgsIHZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc3MjBwJyxcbiAgICBiaXRyYXRlOiBudWxsLFxuICAgIGF1ZGlvQml0cmF0ZTogMTkyLFxuICB9LFxuXG4gIDEyMDoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vZmx2OyBjb2RlY3M9XCJILjI2NCwgYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzcyMHAnLFxuICAgIGJpdHJhdGU6IDIwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAxMjgsXG4gIH0sXG5cbiAgMTI3OiB7XG4gICAgbWltZVR5cGU6ICdhdWRpby90czsgY29kZWNzPVwiYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogbnVsbCxcbiAgICBiaXRyYXRlOiBudWxsLFxuICAgIGF1ZGlvQml0cmF0ZTogOTYsXG4gIH0sXG5cbiAgMTI4OiB7XG4gICAgbWltZVR5cGU6ICdhdWRpby90czsgY29kZWNzPVwiYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogbnVsbCxcbiAgICBiaXRyYXRlOiBudWxsLFxuICAgIGF1ZGlvQml0cmF0ZTogOTYsXG4gIH0sXG5cbiAgMTMyOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby90czsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyNDBwJyxcbiAgICBiaXRyYXRlOiAxNTAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiA0OCxcbiAgfSxcblxuICAxMzM6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMjQwcCcsXG4gICAgYml0cmF0ZTogMjAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxMzQ6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMzYwcCcsXG4gICAgYml0cmF0ZTogMzAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxMzU6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNDgwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxMzY6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNzIwcCcsXG4gICAgYml0cmF0ZTogMTAwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMTM3OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzEwODBwJyxcbiAgICBiaXRyYXRlOiAyNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxMzg6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNDMyMHAnLFxuICAgIGJpdHJhdGU6IDEzNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxMzk6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL21wNDsgY29kZWNzPVwiYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogbnVsbCxcbiAgICBiaXRyYXRlOiBudWxsLFxuICAgIGF1ZGlvQml0cmF0ZTogNDgsXG4gIH0sXG5cbiAgMTQwOiB7XG4gICAgbWltZVR5cGU6ICdhdWRpby9tNGE7IGNvZGVjcz1cImFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6IG51bGwsXG4gICAgYml0cmF0ZTogbnVsbCxcbiAgICBhdWRpb0JpdHJhdGU6IDEyOCxcbiAgfSxcblxuICAxNDE6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL21wNDsgY29kZWNzPVwiYWFjXCInLFxuICAgIHF1YWxpdHlMYWJlbDogbnVsbCxcbiAgICBiaXRyYXRlOiBudWxsLFxuICAgIGF1ZGlvQml0cmF0ZTogMjU2LFxuICB9LFxuXG4gIDE1MToge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vdHM7IGNvZGVjcz1cIkguMjY0LCBhYWNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNzIwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiAyNCxcbiAgfSxcblxuICAxNjA6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMTQ0cCcsXG4gICAgYml0cmF0ZTogMTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAxNzE6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cInZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6IG51bGwsXG4gICAgYml0cmF0ZTogbnVsbCxcbiAgICBhdWRpb0JpdHJhdGU6IDEyOCxcbiAgfSxcblxuICAxNzI6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cInZvcmJpc1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6IG51bGwsXG4gICAgYml0cmF0ZTogbnVsbCxcbiAgICBhdWRpb0JpdHJhdGU6IDE5MixcbiAgfSxcblxuICAyNDI6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyNDBwJyxcbiAgICBiaXRyYXRlOiAxMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDI0Mzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA5XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzM2MHAnLFxuICAgIGJpdHJhdGU6IDI1MDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMjQ0OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNDgwcCcsXG4gICAgYml0cmF0ZTogNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAyNDc6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc3MjBwJyxcbiAgICBiaXRyYXRlOiA3MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDI0ODoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA5XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzEwODBwJyxcbiAgICBiaXRyYXRlOiAxNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAyNDk6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cIm9wdXNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiBudWxsLFxuICAgIGJpdHJhdGU6IG51bGwsXG4gICAgYXVkaW9CaXRyYXRlOiA0OCxcbiAgfSxcblxuICAyNTA6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cIm9wdXNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiBudWxsLFxuICAgIGJpdHJhdGU6IG51bGwsXG4gICAgYXVkaW9CaXRyYXRlOiA2NCxcbiAgfSxcblxuICAyNTE6IHtcbiAgICBtaW1lVHlwZTogJ2F1ZGlvL3dlYm07IGNvZGVjcz1cIm9wdXNcIicsXG4gICAgcXVhbGl0eUxhYmVsOiBudWxsLFxuICAgIGJpdHJhdGU6IG51bGwsXG4gICAgYXVkaW9CaXRyYXRlOiAxNjAsXG4gIH0sXG5cbiAgMjY0OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzE0NDBwJyxcbiAgICBiaXRyYXRlOiA0MDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAyNjY6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL21wNDsgY29kZWNzPVwiSC4yNjRcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMjE2MHAnLFxuICAgIGJpdHJhdGU6IDEyNTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAyNzE6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcxNDQwcCcsXG4gICAgYml0cmF0ZTogOTAwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMjcyOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNDMyMHAnLFxuICAgIGJpdHJhdGU6IDIwMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAyNzg6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcxNDRwIDMwZnBzJyxcbiAgICBiaXRyYXRlOiA4MDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMjk4OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby9tcDQ7IGNvZGVjcz1cIkguMjY0XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzcyMHAnLFxuICAgIGJpdHJhdGU6IDMwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDI5OToge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vbXA0OyBjb2RlY3M9XCJILjI2NFwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcxMDgwcCcsXG4gICAgYml0cmF0ZTogNTUwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzAwOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby90czsgY29kZWNzPVwiSC4yNjQsIGFhY1wiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICc3MjBwJyxcbiAgICBiaXRyYXRlOiAxMzE4MDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogNDgsXG4gIH0sXG5cbiAgMzAyOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNzIwcCBIRlInLFxuICAgIGJpdHJhdGU6IDI1MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDMwMzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA5XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzEwODBwIEhGUicsXG4gICAgYml0cmF0ZTogNTAwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzA4OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMTQ0MHAgSEZSJyxcbiAgICBiaXRyYXRlOiAxMDAwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzEzOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMjE2MHAnLFxuICAgIGJpdHJhdGU6IDEzMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAzMTU6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyMTYwcCBIRlInLFxuICAgIGJpdHJhdGU6IDIwMDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAzMzA6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcxNDRwIEhEUiwgSEZSJyxcbiAgICBiaXRyYXRlOiA4MDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzMxOiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMjQwcCBIRFIsIEhGUicsXG4gICAgYml0cmF0ZTogMTAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAzMzI6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICczNjBwIEhEUiwgSEZSJyxcbiAgICBiaXRyYXRlOiAyNTAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDMzMzoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA5XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzI0MHAgSERSLCBIRlInLFxuICAgIGJpdHJhdGU6IDUwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzM0OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnNzIwcCBIRFIsIEhGUicsXG4gICAgYml0cmF0ZTogMTAwMDAwMCxcbiAgICBhdWRpb0JpdHJhdGU6IG51bGwsXG4gIH0sXG5cbiAgMzM1OiB7XG4gICAgbWltZVR5cGU6ICd2aWRlby93ZWJtOyBjb2RlY3M9XCJWUDlcIicsXG4gICAgcXVhbGl0eUxhYmVsOiAnMTA4MHAgSERSLCBIRlInLFxuICAgIGJpdHJhdGU6IDE1MDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG4gIDMzNjoge1xuICAgIG1pbWVUeXBlOiAndmlkZW8vd2VibTsgY29kZWNzPVwiVlA5XCInLFxuICAgIHF1YWxpdHlMYWJlbDogJzE0NDBwIEhEUiwgSEZSJyxcbiAgICBiaXRyYXRlOiA1MDAwMDAwLFxuICAgIGF1ZGlvQml0cmF0ZTogbnVsbCxcbiAgfSxcblxuICAzMzc6IHtcbiAgICBtaW1lVHlwZTogJ3ZpZGVvL3dlYm07IGNvZGVjcz1cIlZQOVwiJyxcbiAgICBxdWFsaXR5TGFiZWw6ICcyMTYwcCBIRFIsIEhGUicsXG4gICAgYml0cmF0ZTogMTIwMDAwMDAsXG4gICAgYXVkaW9CaXRyYXRlOiBudWxsLFxuICB9LFxuXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/formats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/ytdl-core/lib/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const PassThrough = (__webpack_require__(/*! stream */ \"stream\").PassThrough);\nconst getInfo = __webpack_require__(/*! ./info */ \"(rsc)/./node_modules/ytdl-core/lib/info.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/ytdl-core/lib/utils.js\");\nconst formatUtils = __webpack_require__(/*! ./format-utils */ \"(rsc)/./node_modules/ytdl-core/lib/format-utils.js\");\nconst urlUtils = __webpack_require__(/*! ./url-utils */ \"(rsc)/./node_modules/ytdl-core/lib/url-utils.js\");\nconst sig = __webpack_require__(/*! ./sig */ \"(rsc)/./node_modules/ytdl-core/lib/sig.js\");\nconst miniget = __webpack_require__(/*! miniget */ \"(rsc)/./node_modules/miniget/dist/index.js\");\nconst m3u8stream = __webpack_require__(/*! m3u8stream */ \"(rsc)/./node_modules/m3u8stream/dist/index.js\");\nconst { parseTimestamp } = __webpack_require__(/*! m3u8stream */ \"(rsc)/./node_modules/m3u8stream/dist/index.js\");\n\n\n/**\n * @param {string} link\n * @param {!Object} options\n * @returns {ReadableStream}\n */\nconst ytdl = (link, options) => {\n  const stream = createStream(options);\n  ytdl.getInfo(link, options).then(info => {\n    downloadFromInfoCallback(stream, info, options);\n  }, stream.emit.bind(stream, 'error'));\n  return stream;\n};\nmodule.exports = ytdl;\n\nytdl.getBasicInfo = getInfo.getBasicInfo;\nytdl.getInfo = getInfo.getInfo;\nytdl.chooseFormat = formatUtils.chooseFormat;\nytdl.filterFormats = formatUtils.filterFormats;\nytdl.validateID = urlUtils.validateID;\nytdl.validateURL = urlUtils.validateURL;\nytdl.getURLVideoID = urlUtils.getURLVideoID;\nytdl.getVideoID = urlUtils.getVideoID;\nytdl.cache = {\n  sig: sig.cache,\n  info: getInfo.cache,\n  watch: getInfo.watchPageCache,\n  cookie: getInfo.cookieCache,\n};\nytdl.version = (__webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/ytdl-core/package.json\").version);\n\n\nconst createStream = options => {\n  const stream = new PassThrough({\n    highWaterMark: (options && options.highWaterMark) || 1024 * 512,\n  });\n  stream._destroy = () => { stream.destroyed = true; };\n  return stream;\n};\n\n\nconst pipeAndSetEvents = (req, stream, end) => {\n  // Forward events from the request to the stream.\n  [\n    'abort', 'request', 'response', 'error', 'redirect', 'retry', 'reconnect',\n  ].forEach(event => {\n    req.prependListener(event, stream.emit.bind(stream, event));\n  });\n  req.pipe(stream, { end });\n};\n\n\n/**\n * Chooses a format to download.\n *\n * @param {stream.Readable} stream\n * @param {Object} info\n * @param {Object} options\n */\nconst downloadFromInfoCallback = (stream, info, options) => {\n  options = options || {};\n\n  let err = utils.playError(info.player_response, ['UNPLAYABLE', 'LIVE_STREAM_OFFLINE', 'LOGIN_REQUIRED']);\n  if (err) {\n    stream.emit('error', err);\n    return;\n  }\n\n  if (!info.formats.length) {\n    stream.emit('error', Error('This video is unavailable'));\n    return;\n  }\n\n  let format;\n  try {\n    format = formatUtils.chooseFormat(info.formats, options);\n  } catch (e) {\n    stream.emit('error', e);\n    return;\n  }\n  stream.emit('info', info, format);\n  if (stream.destroyed) { return; }\n\n  let contentLength, downloaded = 0;\n  const ondata = chunk => {\n    downloaded += chunk.length;\n    stream.emit('progress', chunk.length, downloaded, contentLength);\n  };\n\n  if (options.IPv6Block) {\n    options.requestOptions = Object.assign({}, options.requestOptions, {\n      family: 6,\n      localAddress: utils.getRandomIPv6(options.IPv6Block),\n    });\n  }\n\n  // Download the file in chunks, in this case the default is 10MB,\n  // anything over this will cause youtube to throttle the download\n  const dlChunkSize = options.dlChunkSize || 1024 * 1024 * 10;\n  let req;\n  let shouldEnd = true;\n\n  if (format.isHLS || format.isDashMPD) {\n    req = m3u8stream(format.url, {\n      chunkReadahead: +info.live_chunk_readahead,\n      begin: options.begin || (format.isLive && Date.now()),\n      liveBuffer: options.liveBuffer,\n      requestOptions: options.requestOptions,\n      parser: format.isDashMPD ? 'dash-mpd' : 'm3u8',\n      id: format.itag,\n    });\n\n    req.on('progress', (segment, totalSegments) => {\n      stream.emit('progress', segment.size, segment.num, totalSegments);\n    });\n    pipeAndSetEvents(req, stream, shouldEnd);\n  } else {\n    const requestOptions = Object.assign({}, options.requestOptions, {\n      maxReconnects: 6,\n      maxRetries: 3,\n      backoff: { inc: 500, max: 10000 },\n    });\n\n    let shouldBeChunked = dlChunkSize !== 0 && (!format.hasAudio || !format.hasVideo);\n\n    if (shouldBeChunked) {\n      let start = (options.range && options.range.start) || 0;\n      let end = start + dlChunkSize;\n      const rangeEnd = options.range && options.range.end;\n\n      contentLength = options.range ?\n        (rangeEnd ? rangeEnd + 1 : parseInt(format.contentLength)) - start :\n        parseInt(format.contentLength);\n\n      const getNextChunk = () => {\n        if (!rangeEnd && end >= contentLength) end = 0;\n        if (rangeEnd && end > rangeEnd) end = rangeEnd;\n        shouldEnd = !end || end === rangeEnd;\n\n        requestOptions.headers = Object.assign({}, requestOptions.headers, {\n          Range: `bytes=${start}-${end || ''}`,\n        });\n\n        req = miniget(format.url, requestOptions);\n        req.on('data', ondata);\n        req.on('end', () => {\n          if (stream.destroyed) { return; }\n          if (end && end !== rangeEnd) {\n            start = end + 1;\n            end += dlChunkSize;\n            getNextChunk();\n          }\n        });\n        pipeAndSetEvents(req, stream, shouldEnd);\n      };\n      getNextChunk();\n    } else {\n      // Audio only and video only formats don't support begin\n      if (options.begin) {\n        format.url += `&begin=${parseTimestamp(options.begin)}`;\n      }\n      if (options.range && (options.range.start || options.range.end)) {\n        requestOptions.headers = Object.assign({}, requestOptions.headers, {\n          Range: `bytes=${options.range.start || '0'}-${options.range.end || ''}`,\n        });\n      }\n      req = miniget(format.url, requestOptions);\n      req.on('response', res => {\n        if (stream.destroyed) { return; }\n        contentLength = contentLength || parseInt(res.headers['content-length']);\n      });\n      req.on('data', ondata);\n      pipeAndSetEvents(req, stream, shouldEnd);\n    }\n  }\n\n  stream._destroy = () => {\n    stream.destroyed = true;\n    req.destroy();\n    req.end();\n  };\n};\n\n\n/**\n * Can be used to download video after its `info` is gotten through\n * `ytdl.getInfo()`. In case the user might want to look at the\n * `info` object before deciding to download.\n *\n * @param {Object} info\n * @param {!Object} options\n * @returns {ReadableStream}\n */\nytdl.downloadFromInfo = (info, options) => {\n  const stream = createStream(options);\n  if (!info.full) {\n    throw Error('Cannot use `ytdl.downloadFromInfo()` when called ' +\n      'with info from `ytdl.getBasicInfo()`');\n  }\n  setImmediate(() => {\n    downloadFromInfoCallback(stream, info, options);\n  });\n  return stream;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/info-extras.js":
/*!***************************************************!*\
  !*** ./node_modules/ytdl-core/lib/info-extras.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/ytdl-core/lib/utils.js\");\nconst qs = __webpack_require__(/*! querystring */ \"querystring\");\nconst { parseTimestamp } = __webpack_require__(/*! m3u8stream */ \"(rsc)/./node_modules/m3u8stream/dist/index.js\");\n\n\nconst BASE_URL = 'https://www.youtube.com/watch?v=';\nconst TITLE_TO_CATEGORY = {\n  song: { name: 'Music', url: 'https://music.youtube.com/' },\n};\n\nconst getText = obj => obj ? obj.runs ? obj.runs[0].text : obj.simpleText : null;\n\n\n/**\n * Get video media.\n *\n * @param {Object} info\n * @returns {Object}\n */\nexports.getMedia = info => {\n  let media = {};\n  let results = [];\n  try {\n    results = info.response.contents.twoColumnWatchNextResults.results.results.contents;\n  } catch (err) {\n    // Do nothing\n  }\n\n  let result = results.find(v => v.videoSecondaryInfoRenderer);\n  if (!result) { return {}; }\n\n  try {\n    let metadataRows =\n      (result.metadataRowContainer || result.videoSecondaryInfoRenderer.metadataRowContainer)\n        .metadataRowContainerRenderer.rows;\n    for (let row of metadataRows) {\n      if (row.metadataRowRenderer) {\n        let title = getText(row.metadataRowRenderer.title).toLowerCase();\n        let contents = row.metadataRowRenderer.contents[0];\n        media[title] = getText(contents);\n        let runs = contents.runs;\n        if (runs && runs[0].navigationEndpoint) {\n          media[`${title}_url`] = new URL(\n            runs[0].navigationEndpoint.commandMetadata.webCommandMetadata.url, BASE_URL).toString();\n        }\n        if (title in TITLE_TO_CATEGORY) {\n          media.category = TITLE_TO_CATEGORY[title].name;\n          media.category_url = TITLE_TO_CATEGORY[title].url;\n        }\n      } else if (row.richMetadataRowRenderer) {\n        let contents = row.richMetadataRowRenderer.contents;\n        let boxArt = contents\n          .filter(meta => meta.richMetadataRenderer.style === 'RICH_METADATA_RENDERER_STYLE_BOX_ART');\n        for (let { richMetadataRenderer } of boxArt) {\n          let meta = richMetadataRenderer;\n          media.year = getText(meta.subtitle);\n          let type = getText(meta.callToAction).split(' ')[1];\n          media[type] = getText(meta.title);\n          media[`${type}_url`] = new URL(\n            meta.endpoint.commandMetadata.webCommandMetadata.url, BASE_URL).toString();\n          media.thumbnails = meta.thumbnail.thumbnails;\n        }\n        let topic = contents\n          .filter(meta => meta.richMetadataRenderer.style === 'RICH_METADATA_RENDERER_STYLE_TOPIC');\n        for (let { richMetadataRenderer } of topic) {\n          let meta = richMetadataRenderer;\n          media.category = getText(meta.title);\n          media.category_url = new URL(\n            meta.endpoint.commandMetadata.webCommandMetadata.url, BASE_URL).toString();\n        }\n      }\n    }\n  } catch (err) {\n    // Do nothing.\n  }\n\n  return media;\n};\n\n\nconst isVerified = badges => !!(badges && badges.find(b => b.metadataBadgeRenderer.tooltip === 'Verified'));\n\n\n/**\n * Get video author.\n *\n * @param {Object} info\n * @returns {Object}\n */\nexports.getAuthor = info => {\n  let channelId, thumbnails = [], subscriberCount, verified = false;\n  try {\n    let results = info.response.contents.twoColumnWatchNextResults.results.results.contents;\n    let v = results.find(v2 =>\n      v2.videoSecondaryInfoRenderer &&\n      v2.videoSecondaryInfoRenderer.owner &&\n      v2.videoSecondaryInfoRenderer.owner.videoOwnerRenderer);\n    let videoOwnerRenderer = v.videoSecondaryInfoRenderer.owner.videoOwnerRenderer;\n    channelId = videoOwnerRenderer.navigationEndpoint.browseEndpoint.browseId;\n    thumbnails = videoOwnerRenderer.thumbnail.thumbnails.map(thumbnail => {\n      thumbnail.url = new URL(thumbnail.url, BASE_URL).toString();\n      return thumbnail;\n    });\n    subscriberCount = utils.parseAbbreviatedNumber(getText(videoOwnerRenderer.subscriberCountText));\n    verified = isVerified(videoOwnerRenderer.badges);\n  } catch (err) {\n    // Do nothing.\n  }\n  try {\n    let videoDetails = info.player_response.microformat && info.player_response.microformat.playerMicroformatRenderer;\n    let id = (videoDetails && videoDetails.channelId) || channelId || info.player_response.videoDetails.channelId;\n    let author = {\n      id: id,\n      name: videoDetails ? videoDetails.ownerChannelName : info.player_response.videoDetails.author,\n      user: videoDetails ? videoDetails.ownerProfileUrl.split('/').slice(-1)[0] : null,\n      channel_url: `https://www.youtube.com/channel/${id}`,\n      external_channel_url: videoDetails ? `https://www.youtube.com/channel/${videoDetails.externalChannelId}` : '',\n      user_url: videoDetails ? new URL(videoDetails.ownerProfileUrl, BASE_URL).toString() : '',\n      thumbnails,\n      verified,\n      subscriber_count: subscriberCount,\n    };\n    if (thumbnails.length) {\n      utils.deprecate(author, 'avatar', author.thumbnails[0].url, 'author.avatar', 'author.thumbnails[0].url');\n    }\n    return author;\n  } catch (err) {\n    return {};\n  }\n};\n\nconst parseRelatedVideo = (details, rvsParams) => {\n  if (!details) return;\n  try {\n    let viewCount = getText(details.viewCountText);\n    let shortViewCount = getText(details.shortViewCountText);\n    let rvsDetails = rvsParams.find(elem => elem.id === details.videoId);\n    if (!/^\\d/.test(shortViewCount)) {\n      shortViewCount = (rvsDetails && rvsDetails.short_view_count_text) || '';\n    }\n    viewCount = (/^\\d/.test(viewCount) ? viewCount : shortViewCount).split(' ')[0];\n    let browseEndpoint = details.shortBylineText.runs[0].navigationEndpoint.browseEndpoint;\n    let channelId = browseEndpoint.browseId;\n    let name = getText(details.shortBylineText);\n    let user = (browseEndpoint.canonicalBaseUrl || '').split('/').slice(-1)[0];\n    let video = {\n      id: details.videoId,\n      title: getText(details.title),\n      published: getText(details.publishedTimeText),\n      author: {\n        id: channelId,\n        name,\n        user,\n        channel_url: `https://www.youtube.com/channel/${channelId}`,\n        user_url: `https://www.youtube.com/user/${user}`,\n        thumbnails: details.channelThumbnail.thumbnails.map(thumbnail => {\n          thumbnail.url = new URL(thumbnail.url, BASE_URL).toString();\n          return thumbnail;\n        }),\n        verified: isVerified(details.ownerBadges),\n\n        [Symbol.toPrimitive]() {\n          console.warn(`\\`relatedVideo.author\\` will be removed in a near future release, ` +\n            `use \\`relatedVideo.author.name\\` instead.`);\n          return video.author.name;\n        },\n\n      },\n      short_view_count_text: shortViewCount.split(' ')[0],\n      view_count: viewCount.replace(/,/g, ''),\n      length_seconds: details.lengthText ?\n        Math.floor(parseTimestamp(getText(details.lengthText)) / 1000) :\n        rvsParams && `${rvsParams.length_seconds}`,\n      thumbnails: details.thumbnail.thumbnails,\n      richThumbnails:\n        details.richThumbnail ?\n          details.richThumbnail.movingThumbnailRenderer.movingThumbnailDetails.thumbnails : [],\n      isLive: !!(details.badges && details.badges.find(b => b.metadataBadgeRenderer.label === 'LIVE NOW')),\n    };\n\n    utils.deprecate(video, 'author_thumbnail', video.author.thumbnails[0].url,\n      'relatedVideo.author_thumbnail', 'relatedVideo.author.thumbnails[0].url');\n    utils.deprecate(video, 'ucid', video.author.id, 'relatedVideo.ucid', 'relatedVideo.author.id');\n    utils.deprecate(video, 'video_thumbnail', video.thumbnails[0].url,\n      'relatedVideo.video_thumbnail', 'relatedVideo.thumbnails[0].url');\n    return video;\n  } catch (err) {\n    // Skip.\n  }\n};\n\n/**\n * Get related videos.\n *\n * @param {Object} info\n * @returns {Array.<Object>}\n */\nexports.getRelatedVideos = info => {\n  let rvsParams = [], secondaryResults = [];\n  try {\n    rvsParams = info.response.webWatchNextResponseExtensionData.relatedVideoArgs.split(',').map(e => qs.parse(e));\n  } catch (err) {\n    // Do nothing.\n  }\n  try {\n    secondaryResults = info.response.contents.twoColumnWatchNextResults.secondaryResults.secondaryResults.results;\n  } catch (err) {\n    return [];\n  }\n  let videos = [];\n  for (let result of secondaryResults || []) {\n    let details = result.compactVideoRenderer;\n    if (details) {\n      let video = parseRelatedVideo(details, rvsParams);\n      if (video) videos.push(video);\n    } else {\n      let autoplay = result.compactAutoplayRenderer || result.itemSectionRenderer;\n      if (!autoplay || !Array.isArray(autoplay.contents)) continue;\n      for (let content of autoplay.contents) {\n        let video = parseRelatedVideo(content.compactVideoRenderer, rvsParams);\n        if (video) videos.push(video);\n      }\n    }\n  }\n  return videos;\n};\n\n/**\n * Get like count.\n *\n * @param {Object} info\n * @returns {number}\n */\nexports.getLikes = info => {\n  try {\n    let contents = info.response.contents.twoColumnWatchNextResults.results.results.contents;\n    let video = contents.find(r => r.videoPrimaryInfoRenderer);\n    let buttons = video.videoPrimaryInfoRenderer.videoActions.menuRenderer.topLevelButtons;\n    let like = buttons.find(b => b.toggleButtonRenderer &&\n      b.toggleButtonRenderer.defaultIcon.iconType === 'LIKE');\n    return parseInt(like.toggleButtonRenderer.defaultText.accessibility.accessibilityData.label.replace(/\\D+/g, ''));\n  } catch (err) {\n    return null;\n  }\n};\n\n/**\n * Get dislike count.\n *\n * @param {Object} info\n * @returns {number}\n */\nexports.getDislikes = info => {\n  try {\n    let contents = info.response.contents.twoColumnWatchNextResults.results.results.contents;\n    let video = contents.find(r => r.videoPrimaryInfoRenderer);\n    let buttons = video.videoPrimaryInfoRenderer.videoActions.menuRenderer.topLevelButtons;\n    let dislike = buttons.find(b => b.toggleButtonRenderer &&\n      b.toggleButtonRenderer.defaultIcon.iconType === 'DISLIKE');\n    return parseInt(dislike.toggleButtonRenderer.defaultText.accessibility.accessibilityData.label.replace(/\\D+/g, ''));\n  } catch (err) {\n    return null;\n  }\n};\n\n/**\n * Cleans up a few fields on `videoDetails`.\n *\n * @param {Object} videoDetails\n * @param {Object} info\n * @returns {Object}\n */\nexports.cleanVideoDetails = (videoDetails, info) => {\n  videoDetails.thumbnails = videoDetails.thumbnail.thumbnails;\n  delete videoDetails.thumbnail;\n  utils.deprecate(videoDetails, 'thumbnail', { thumbnails: videoDetails.thumbnails },\n    'videoDetails.thumbnail.thumbnails', 'videoDetails.thumbnails');\n  videoDetails.description = videoDetails.shortDescription || getText(videoDetails.description);\n  delete videoDetails.shortDescription;\n  utils.deprecate(videoDetails, 'shortDescription', videoDetails.description,\n    'videoDetails.shortDescription', 'videoDetails.description');\n\n  // Use more reliable `lengthSeconds` from `playerMicroformatRenderer`.\n  videoDetails.lengthSeconds =\n    (info.player_response.microformat &&\n    info.player_response.microformat.playerMicroformatRenderer.lengthSeconds) ||\n    info.player_response.videoDetails.lengthSeconds;\n  return videoDetails;\n};\n\n/**\n * Get storyboards info.\n *\n * @param {Object} info\n * @returns {Array.<Object>}\n */\nexports.getStoryboards = info => {\n  const parts = info.player_response.storyboards &&\n    info.player_response.storyboards.playerStoryboardSpecRenderer &&\n    info.player_response.storyboards.playerStoryboardSpecRenderer.spec &&\n    info.player_response.storyboards.playerStoryboardSpecRenderer.spec.split('|');\n\n  if (!parts) return [];\n\n  const url = new URL(parts.shift());\n\n  return parts.map((part, i) => {\n    let [\n      thumbnailWidth,\n      thumbnailHeight,\n      thumbnailCount,\n      columns,\n      rows,\n      interval,\n      nameReplacement,\n      sigh,\n    ] = part.split('#');\n\n    url.searchParams.set('sigh', sigh);\n\n    thumbnailCount = parseInt(thumbnailCount, 10);\n    columns = parseInt(columns, 10);\n    rows = parseInt(rows, 10);\n\n    const storyboardCount = Math.ceil(thumbnailCount / (columns * rows));\n\n    return {\n      templateUrl: url.toString().replace('$L', i).replace('$N', nameReplacement),\n      thumbnailWidth: parseInt(thumbnailWidth, 10),\n      thumbnailHeight: parseInt(thumbnailHeight, 10),\n      thumbnailCount,\n      interval: parseInt(interval, 10),\n      columns,\n      rows,\n      storyboardCount,\n    };\n  });\n};\n\n/**\n * Get chapters info.\n *\n * @param {Object} info\n * @returns {Array.<Object>}\n */\nexports.getChapters = info => {\n  const playerOverlayRenderer = info.response &&\n    info.response.playerOverlays &&\n    info.response.playerOverlays.playerOverlayRenderer;\n  const playerBar = playerOverlayRenderer &&\n    playerOverlayRenderer.decoratedPlayerBarRenderer &&\n    playerOverlayRenderer.decoratedPlayerBarRenderer.decoratedPlayerBarRenderer &&\n    playerOverlayRenderer.decoratedPlayerBarRenderer.decoratedPlayerBarRenderer.playerBar;\n  const markersMap = playerBar &&\n    playerBar.multiMarkersPlayerBarRenderer &&\n    playerBar.multiMarkersPlayerBarRenderer.markersMap;\n  const marker = Array.isArray(markersMap) && markersMap.find(m => m.value && Array.isArray(m.value.chapters));\n  if (!marker) return [];\n  const chapters = marker.value.chapters;\n\n  return chapters.map(chapter => ({\n    title: getText(chapter.chapterRenderer.title),\n    start_time: chapter.chapterRenderer.timeRangeStartMillis / 1000,\n  }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/info-extras.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/info.js":
/*!********************************************!*\
  !*** ./node_modules/ytdl-core/lib/info.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst sax = __webpack_require__(/*! sax */ \"(rsc)/./node_modules/sax/lib/sax.js\");\nconst miniget = __webpack_require__(/*! miniget */ \"(rsc)/./node_modules/miniget/dist/index.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/ytdl-core/lib/utils.js\");\n// Forces Node JS version of setTimeout for Electron based applications\nconst { setTimeout } = __webpack_require__(/*! timers */ \"timers\");\nconst formatUtils = __webpack_require__(/*! ./format-utils */ \"(rsc)/./node_modules/ytdl-core/lib/format-utils.js\");\nconst urlUtils = __webpack_require__(/*! ./url-utils */ \"(rsc)/./node_modules/ytdl-core/lib/url-utils.js\");\nconst extras = __webpack_require__(/*! ./info-extras */ \"(rsc)/./node_modules/ytdl-core/lib/info-extras.js\");\nconst sig = __webpack_require__(/*! ./sig */ \"(rsc)/./node_modules/ytdl-core/lib/sig.js\");\nconst Cache = __webpack_require__(/*! ./cache */ \"(rsc)/./node_modules/ytdl-core/lib/cache.js\");\n\n\nconst BASE_URL = 'https://www.youtube.com/watch?v=';\n\n\n// Cached for storing basic/full info.\nexports.cache = new Cache();\nexports.cookieCache = new Cache(1000 * 60 * 60 * 24);\nexports.watchPageCache = new Cache();\n// Cache for cver used in getVideoInfoPage\nlet cver = '2.20210622.10.00';\n\n\n// Special error class used to determine if an error is unrecoverable,\n// as in, ytdl-core should not try again to fetch the video metadata.\n// In this case, the video is usually unavailable in some way.\nclass UnrecoverableError extends Error {}\n\n\n// List of URLs that show up in `notice_url` for age restricted videos.\nconst AGE_RESTRICTED_URLS = [\n  'support.google.com/youtube/?p=age_restrictions',\n  'youtube.com/t/community_guidelines',\n];\n\n\n/**\n * Gets info from a video without getting additional formats.\n *\n * @param {string} id\n * @param {Object} options\n * @returns {Promise<Object>}\n*/\nexports.getBasicInfo = async(id, options) => {\n  if (options.IPv6Block) {\n    options.requestOptions = Object.assign({}, options.requestOptions, {\n      family: 6,\n      localAddress: utils.getRandomIPv6(options.IPv6Block),\n    });\n  }\n  const retryOptions = Object.assign({}, miniget.defaultOptions, options.requestOptions);\n  options.requestOptions = Object.assign({}, options.requestOptions, {});\n  options.requestOptions.headers = Object.assign({},\n    {\n      // eslint-disable-next-line max-len\n      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Safari/537.36',\n    }, options.requestOptions.headers);\n  const validate = info => {\n    let playErr = utils.playError(info.player_response, ['ERROR'], UnrecoverableError);\n    let privateErr = privateVideoError(info.player_response);\n    if (playErr || privateErr) {\n      throw playErr || privateErr;\n    }\n    return info && info.player_response && (\n      info.player_response.streamingData || isRental(info.player_response) || isNotYetBroadcasted(info.player_response)\n    );\n  };\n  let info = await pipeline([id, options], validate, retryOptions, [\n    getWatchHTMLPage,\n    getWatchJSONPage,\n    getVideoInfoPage,\n  ]);\n\n  Object.assign(info, {\n    formats: parseFormats(info.player_response),\n    related_videos: extras.getRelatedVideos(info),\n  });\n\n  // Add additional properties to info.\n  const media = extras.getMedia(info);\n  const additional = {\n    author: extras.getAuthor(info),\n    media,\n    likes: extras.getLikes(info),\n    dislikes: extras.getDislikes(info),\n    age_restricted: !!(media && AGE_RESTRICTED_URLS.some(url =>\n      Object.values(media).some(v => typeof v === 'string' && v.includes(url)))\n    ),\n\n    // Give the standard link to the video.\n    video_url: BASE_URL + id,\n    storyboards: extras.getStoryboards(info),\n    chapters: extras.getChapters(info),\n  };\n\n  info.videoDetails = extras.cleanVideoDetails(Object.assign({},\n    info.player_response && info.player_response.microformat &&\n    info.player_response.microformat.playerMicroformatRenderer,\n    info.player_response && info.player_response.videoDetails, additional), info);\n\n  return info;\n};\n\nconst privateVideoError = player_response => {\n  let playability = player_response && player_response.playabilityStatus;\n  if (playability && playability.status === 'LOGIN_REQUIRED' && playability.messages &&\n    playability.messages.filter(m => /This is a private video/.test(m)).length) {\n    return new UnrecoverableError(playability.reason || (playability.messages && playability.messages[0]));\n  } else {\n    return null;\n  }\n};\n\n\nconst isRental = player_response => {\n  let playability = player_response.playabilityStatus;\n  return playability && playability.status === 'UNPLAYABLE' &&\n    playability.errorScreen && playability.errorScreen.playerLegacyDesktopYpcOfferRenderer;\n};\n\n\nconst isNotYetBroadcasted = player_response => {\n  let playability = player_response.playabilityStatus;\n  return playability && playability.status === 'LIVE_STREAM_OFFLINE';\n};\n\n\nconst getWatchHTMLURL = (id, options) => `${BASE_URL + id}&hl=${options.lang || 'en'}`;\nconst getWatchHTMLPageBody = (id, options) => {\n  const url = getWatchHTMLURL(id, options);\n  return exports.watchPageCache.getOrSet(url, () => utils.exposedMiniget(url, options).text());\n};\n\n\nconst EMBED_URL = 'https://www.youtube.com/embed/';\nconst getEmbedPageBody = (id, options) => {\n  const embedUrl = `${EMBED_URL + id}?hl=${options.lang || 'en'}`;\n  return utils.exposedMiniget(embedUrl, options).text();\n};\n\n\nconst getHTML5player = body => {\n  let html5playerRes =\n    /<script\\s+src=\"([^\"]+)\"(?:\\s+type=\"text\\/javascript\")?\\s+name=\"player_ias\\/base\"\\s*>|\"jsUrl\":\"([^\"]+)\"/\n      .exec(body);\n  return html5playerRes ? html5playerRes[1] || html5playerRes[2] : null;\n};\n\n\nconst getIdentityToken = (id, options, key, throwIfNotFound) =>\n  exports.cookieCache.getOrSet(key, async() => {\n    let page = await getWatchHTMLPageBody(id, options);\n    let match = page.match(/([\"'])ID_TOKEN\\1[:,]\\s?\"([^\"]+)\"/);\n    if (!match && throwIfNotFound) {\n      throw new UnrecoverableError('Cookie header used in request, but unable to find YouTube identity token');\n    }\n    return match && match[2];\n  });\n\n\n/**\n * Goes through each endpoint in the pipeline, retrying on failure if the error is recoverable.\n * If unable to succeed with one endpoint, moves onto the next one.\n *\n * @param {Array.<Object>} args\n * @param {Function} validate\n * @param {Object} retryOptions\n * @param {Array.<Function>} endpoints\n * @returns {[Object, Object, Object]}\n */\nconst pipeline = async(args, validate, retryOptions, endpoints) => {\n  let info;\n  for (let func of endpoints) {\n    try {\n      const newInfo = await retryFunc(func, args.concat([info]), retryOptions);\n      if (newInfo.player_response) {\n        newInfo.player_response.videoDetails = assign(\n          info && info.player_response && info.player_response.videoDetails,\n          newInfo.player_response.videoDetails);\n        newInfo.player_response = assign(info && info.player_response, newInfo.player_response);\n      }\n      info = assign(info, newInfo);\n      if (validate(info, false)) {\n        break;\n      }\n    } catch (err) {\n      if (err instanceof UnrecoverableError || func === endpoints[endpoints.length - 1]) {\n        throw err;\n      }\n      // Unable to find video metadata... so try next endpoint.\n    }\n  }\n  return info;\n};\n\n\n/**\n * Like Object.assign(), but ignores `null` and `undefined` from `source`.\n *\n * @param {Object} target\n * @param {Object} source\n * @returns {Object}\n */\nconst assign = (target, source) => {\n  if (!target || !source) { return target || source; }\n  for (let [key, value] of Object.entries(source)) {\n    if (value !== null && value !== undefined) {\n      target[key] = value;\n    }\n  }\n  return target;\n};\n\n\n/**\n * Given a function, calls it with `args` until it's successful,\n * or until it encounters an unrecoverable error.\n * Currently, any error from miniget is considered unrecoverable. Errors such as\n * too many redirects, invalid URL, status code 404, status code 502.\n *\n * @param {Function} func\n * @param {Array.<Object>} args\n * @param {Object} options\n * @param {number} options.maxRetries\n * @param {Object} options.backoff\n * @param {number} options.backoff.inc\n */\nconst retryFunc = async(func, args, options) => {\n  let currentTry = 0, result;\n  while (currentTry <= options.maxRetries) {\n    try {\n      result = await func(...args);\n      break;\n    } catch (err) {\n      if (err instanceof UnrecoverableError ||\n        (err instanceof miniget.MinigetError && err.statusCode < 500) || currentTry >= options.maxRetries) {\n        throw err;\n      }\n      let wait = Math.min(++currentTry * options.backoff.inc, options.backoff.max);\n      await new Promise(resolve => setTimeout(resolve, wait));\n    }\n  }\n  return result;\n};\n\n\nconst jsonClosingChars = /^[)\\]}'\\s]+/;\nconst parseJSON = (source, varName, json) => {\n  if (!json || typeof json === 'object') {\n    return json;\n  } else {\n    try {\n      json = json.replace(jsonClosingChars, '');\n      return JSON.parse(json);\n    } catch (err) {\n      throw Error(`Error parsing ${varName} in ${source}: ${err.message}`);\n    }\n  }\n};\n\n\nconst findJSON = (source, varName, body, left, right, prependJSON) => {\n  let jsonStr = utils.between(body, left, right);\n  if (!jsonStr) {\n    throw Error(`Could not find ${varName} in ${source}`);\n  }\n  return parseJSON(source, varName, utils.cutAfterJS(`${prependJSON}${jsonStr}`));\n};\n\n\nconst findPlayerResponse = (source, info) => {\n  const player_response = info && (\n    (info.args && info.args.player_response) ||\n    info.player_response || info.playerResponse || info.embedded_player_response);\n  return parseJSON(source, 'player_response', player_response);\n};\n\n\nconst getWatchJSONURL = (id, options) => `${getWatchHTMLURL(id, options)}&pbj=1`;\nconst getWatchJSONPage = async(id, options) => {\n  const reqOptions = Object.assign({ headers: {} }, options.requestOptions);\n  let cookie = reqOptions.headers.Cookie || reqOptions.headers.cookie;\n  reqOptions.headers = Object.assign({\n    'x-youtube-client-name': '1',\n    'x-youtube-client-version': cver,\n    'x-youtube-identity-token': exports.cookieCache.get(cookie || 'browser') || '',\n  }, reqOptions.headers);\n\n  const setIdentityToken = async(key, throwIfNotFound) => {\n    if (reqOptions.headers['x-youtube-identity-token']) { return; }\n    reqOptions.headers['x-youtube-identity-token'] = await getIdentityToken(id, options, key, throwIfNotFound);\n  };\n\n  if (cookie) {\n    await setIdentityToken(cookie, true);\n  }\n\n  const jsonUrl = getWatchJSONURL(id, options);\n  const body = await utils.exposedMiniget(jsonUrl, options, reqOptions).text();\n  let parsedBody = parseJSON('watch.json', 'body', body);\n  if (parsedBody.reload === 'now') {\n    await setIdentityToken('browser', false);\n  }\n  if (parsedBody.reload === 'now' || !Array.isArray(parsedBody)) {\n    throw Error('Unable to retrieve video metadata in watch.json');\n  }\n  let info = parsedBody.reduce((part, curr) => Object.assign(curr, part), {});\n  info.player_response = findPlayerResponse('watch.json', info);\n  info.html5player = info.player && info.player.assets && info.player.assets.js;\n\n  return info;\n};\n\n\nconst getWatchHTMLPage = async(id, options) => {\n  let body = await getWatchHTMLPageBody(id, options);\n  let info = { page: 'watch' };\n  try {\n    cver = utils.between(body, '{\"key\":\"cver\",\"value\":\"', '\"}');\n    info.player_response = findJSON('watch.html', 'player_response',\n      body, /\\bytInitialPlayerResponse\\s*=\\s*\\{/i, '</script>', '{');\n  } catch (err) {\n    let args = findJSON('watch.html', 'player_response', body, /\\bytplayer\\.config\\s*=\\s*{/, '</script>', '{');\n    info.player_response = findPlayerResponse('watch.html', args);\n  }\n  info.response = findJSON('watch.html', 'response', body, /\\bytInitialData(\"\\])?\\s*=\\s*\\{/i, '</script>', '{');\n  info.html5player = getHTML5player(body);\n  return info;\n};\n\n\nconst INFO_HOST = 'www.youtube.com';\nconst INFO_PATH = '/get_video_info';\nconst VIDEO_EURL = 'https://youtube.googleapis.com/v/';\nconst getVideoInfoPage = async(id, options) => {\n  const url = new URL(`https://${INFO_HOST}${INFO_PATH}`);\n  url.searchParams.set('video_id', id);\n  url.searchParams.set('c', 'TVHTML5');\n  url.searchParams.set('cver', `7${cver.substr(1)}`);\n  url.searchParams.set('eurl', VIDEO_EURL + id);\n  url.searchParams.set('ps', 'default');\n  url.searchParams.set('gl', 'US');\n  url.searchParams.set('hl', options.lang || 'en');\n  url.searchParams.set('html5', '1');\n  const body = await utils.exposedMiniget(url.toString(), options).text();\n  let info = querystring.parse(body);\n  info.player_response = findPlayerResponse('get_video_info', info);\n  return info;\n};\n\n\n/**\n * @param {Object} player_response\n * @returns {Array.<Object>}\n */\nconst parseFormats = player_response => {\n  let formats = [];\n  if (player_response && player_response.streamingData) {\n    formats = formats\n      .concat(player_response.streamingData.formats || [])\n      .concat(player_response.streamingData.adaptiveFormats || []);\n  }\n  return formats;\n};\n\n\n/**\n * Gets info from a video additional formats and deciphered URLs.\n *\n * @param {string} id\n * @param {Object} options\n * @returns {Promise<Object>}\n */\nexports.getInfo = async(id, options) => {\n  let info = await exports.getBasicInfo(id, options);\n  const hasManifest =\n    info.player_response && info.player_response.streamingData && (\n      info.player_response.streamingData.dashManifestUrl ||\n      info.player_response.streamingData.hlsManifestUrl\n    );\n  let funcs = [];\n  if (info.formats.length) {\n    info.html5player = info.html5player ||\n      getHTML5player(await getWatchHTMLPageBody(id, options)) || getHTML5player(await getEmbedPageBody(id, options));\n    if (!info.html5player) {\n      throw Error('Unable to find html5player file');\n    }\n    const html5player = new URL(info.html5player, BASE_URL).toString();\n    funcs.push(sig.decipherFormats(info.formats, html5player, options));\n  }\n  if (hasManifest && info.player_response.streamingData.dashManifestUrl) {\n    let url = info.player_response.streamingData.dashManifestUrl;\n    funcs.push(getDashManifest(url, options));\n  }\n  if (hasManifest && info.player_response.streamingData.hlsManifestUrl) {\n    let url = info.player_response.streamingData.hlsManifestUrl;\n    funcs.push(getM3U8(url, options));\n  }\n\n  let results = await Promise.all(funcs);\n  info.formats = Object.values(Object.assign({}, ...results));\n  info.formats = info.formats.map(formatUtils.addFormatMeta);\n  info.formats.sort(formatUtils.sortFormats);\n  info.full = true;\n  return info;\n};\n\n\n/**\n * Gets additional DASH formats.\n *\n * @param {string} url\n * @param {Object} options\n * @returns {Promise<Array.<Object>>}\n */\nconst getDashManifest = (url, options) => new Promise((resolve, reject) => {\n  let formats = {};\n  const parser = sax.parser(false);\n  parser.onerror = reject;\n  let adaptationSet;\n  parser.onopentag = node => {\n    if (node.name === 'ADAPTATIONSET') {\n      adaptationSet = node.attributes;\n    } else if (node.name === 'REPRESENTATION') {\n      const itag = parseInt(node.attributes.ID);\n      if (!isNaN(itag)) {\n        formats[url] = Object.assign({\n          itag, url,\n          bitrate: parseInt(node.attributes.BANDWIDTH),\n          mimeType: `${adaptationSet.MIMETYPE}; codecs=\"${node.attributes.CODECS}\"`,\n        }, node.attributes.HEIGHT ? {\n          width: parseInt(node.attributes.WIDTH),\n          height: parseInt(node.attributes.HEIGHT),\n          fps: parseInt(node.attributes.FRAMERATE),\n        } : {\n          audioSampleRate: node.attributes.AUDIOSAMPLINGRATE,\n        });\n      }\n    }\n  };\n  parser.onend = () => { resolve(formats); };\n  const req = utils.exposedMiniget(new URL(url, BASE_URL).toString(), options);\n  req.setEncoding('utf8');\n  req.on('error', reject);\n  req.on('data', chunk => { parser.write(chunk); });\n  req.on('end', parser.close.bind(parser));\n});\n\n\n/**\n * Gets additional formats.\n *\n * @param {string} url\n * @param {Object} options\n * @returns {Promise<Array.<Object>>}\n */\nconst getM3U8 = async(url, options) => {\n  url = new URL(url, BASE_URL);\n  const body = await utils.exposedMiniget(url.toString(), options).text();\n  let formats = {};\n  body\n    .split('\\n')\n    .filter(line => /^https?:\\/\\//.test(line))\n    .forEach(line => {\n      const itag = parseInt(line.match(/\\/itag\\/(\\d+)\\//)[1]);\n      formats[line] = { itag, url: line };\n    });\n  return formats;\n};\n\n\n// Cache get info functions.\n// In case a user wants to get a video's info before downloading.\nfor (let funcName of ['getBasicInfo', 'getInfo']) {\n  /**\n   * @param {string} link\n   * @param {Object} options\n   * @returns {Promise<Object>}\n   */\n  const func = exports[funcName];\n  exports[funcName] = async(link, options = {}) => {\n    utils.checkForUpdates();\n    let id = await urlUtils.getVideoID(link);\n    const key = [funcName, id, options.lang].join('-');\n    return exports.cache.getOrSet(key, () => func(id, options));\n  };\n}\n\n\n// Export a few helpers.\nexports.validateID = urlUtils.validateID;\nexports.validateURL = urlUtils.validateURL;\nexports.getURLVideoID = urlUtils.getURLVideoID;\nexports.getVideoID = urlUtils.getVideoID;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/info.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/sig.js":
/*!*******************************************!*\
  !*** ./node_modules/ytdl-core/lib/sig.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst Cache = __webpack_require__(/*! ./cache */ \"(rsc)/./node_modules/ytdl-core/lib/cache.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/ytdl-core/lib/utils.js\");\nconst vm = __webpack_require__(/*! vm */ \"vm\");\n\n// A shared cache to keep track of html5player js functions.\nexports.cache = new Cache();\n\n/**\n * Extract signature deciphering and n parameter transform functions from html5player file.\n *\n * @param {string} html5playerfile\n * @param {Object} options\n * @returns {Promise<Array.<string>>}\n */\nexports.getFunctions = (html5playerfile, options) => exports.cache.getOrSet(html5playerfile, async() => {\n  const body = await utils.exposedMiniget(html5playerfile, options).text();\n  const functions = exports.extractFunctions(body);\n  if (!functions || !functions.length) {\n    throw Error('Could not extract functions');\n  }\n  exports.cache.set(html5playerfile, functions);\n  return functions;\n});\n\n/**\n * Extracts the actions that should be taken to decipher a signature\n * and tranform the n parameter\n *\n * @param {string} body\n * @returns {Array.<string>}\n */\nexports.extractFunctions = body => {\n  const functions = [];\n  const extractManipulations = caller => {\n    const functionName = utils.between(caller, `a=a.split(\"\");`, `.`);\n    if (!functionName) return '';\n    const functionStart = `var ${functionName}={`;\n    const ndx = body.indexOf(functionStart);\n    if (ndx < 0) return '';\n    const subBody = body.slice(ndx + functionStart.length - 1);\n    return `var ${functionName}=${utils.cutAfterJS(subBody)}`;\n  };\n  const extractDecipher = () => {\n    const functionName = utils.between(body, `a.set(\"alr\",\"yes\");c&&(c=`, `(decodeURIC`);\n    if (functionName && functionName.length) {\n      const functionStart = `${functionName}=function(a)`;\n      const ndx = body.indexOf(functionStart);\n      if (ndx >= 0) {\n        const subBody = body.slice(ndx + functionStart.length);\n        let functionBody = `var ${functionStart}${utils.cutAfterJS(subBody)}`;\n        functionBody = `${extractManipulations(functionBody)};${functionBody};${functionName}(sig);`;\n        functions.push(functionBody);\n      }\n    }\n  };\n  const extractNCode = () => {\n    let functionName = utils.between(body, `&&(b=a.get(\"n\"))&&(b=`, `(b)`);\n    if (functionName.includes('[')) functionName = utils.between(body, `var ${functionName.split('[')[0]}=[`, `]`);\n    if (functionName && functionName.length) {\n      const functionStart = `${functionName}=function(a)`;\n      const ndx = body.indexOf(functionStart);\n      if (ndx >= 0) {\n        const subBody = body.slice(ndx + functionStart.length);\n        const functionBody = `var ${functionStart}${utils.cutAfterJS(subBody)};${functionName}(ncode);`;\n        functions.push(functionBody);\n      }\n    }\n  };\n  extractDecipher();\n  extractNCode();\n  return functions;\n};\n\n/**\n * Apply decipher and n-transform to individual format\n *\n * @param {Object} format\n * @param {vm.Script} decipherScript\n * @param {vm.Script} nTransformScript\n */\nexports.setDownloadURL = (format, decipherScript, nTransformScript) => {\n  const decipher = url => {\n    const args = querystring.parse(url);\n    if (!args.s || !decipherScript) return args.url;\n    const components = new URL(decodeURIComponent(args.url));\n    components.searchParams.set(args.sp ? args.sp : 'signature',\n      decipherScript.runInNewContext({ sig: decodeURIComponent(args.s) }));\n    return components.toString();\n  };\n  const ncode = url => {\n    const components = new URL(decodeURIComponent(url));\n    const n = components.searchParams.get('n');\n    if (!n || !nTransformScript) return url;\n    components.searchParams.set('n', nTransformScript.runInNewContext({ ncode: n }));\n    return components.toString();\n  };\n  const cipher = !format.url;\n  const url = format.url || format.signatureCipher || format.cipher;\n  format.url = cipher ? ncode(decipher(url)) : ncode(url);\n  delete format.signatureCipher;\n  delete format.cipher;\n};\n\n/**\n * Applies decipher and n parameter transforms to all format URL's.\n *\n * @param {Array.<Object>} formats\n * @param {string} html5player\n * @param {Object} options\n */\nexports.decipherFormats = async(formats, html5player, options) => {\n  let decipheredFormats = {};\n  let functions = await exports.getFunctions(html5player, options);\n  const decipherScript = functions.length ? new vm.Script(functions[0]) : null;\n  const nTransformScript = functions.length > 1 ? new vm.Script(functions[1]) : null;\n  formats.forEach(format => {\n    exports.setDownloadURL(format, decipherScript, nTransformScript);\n    decipheredFormats[format.url] = format;\n  });\n  return decipheredFormats;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/sig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/url-utils.js":
/*!*************************************************!*\
  !*** ./node_modules/ytdl-core/lib/url-utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Get video ID.\n *\n * There are a few type of video URL formats.\n *  - https://www.youtube.com/watch?v=VIDEO_ID\n *  - https://m.youtube.com/watch?v=VIDEO_ID\n *  - https://youtu.be/VIDEO_ID\n *  - https://www.youtube.com/v/VIDEO_ID\n *  - https://www.youtube.com/embed/VIDEO_ID\n *  - https://music.youtube.com/watch?v=VIDEO_ID\n *  - https://gaming.youtube.com/watch?v=VIDEO_ID\n *\n * @param {string} link\n * @return {string}\n * @throws {Error} If unable to find a id\n * @throws {TypeError} If videoid doesn't match specs\n */\nconst validQueryDomains = new Set([\n  'youtube.com',\n  'www.youtube.com',\n  'm.youtube.com',\n  'music.youtube.com',\n  'gaming.youtube.com',\n]);\nconst validPathDomains = /^https?:\\/\\/(youtu\\.be\\/|(www\\.)?youtube\\.com\\/(embed|v|shorts)\\/)/;\nexports.getURLVideoID = link => {\n  const parsed = new URL(link.trim());\n  let id = parsed.searchParams.get('v');\n  if (validPathDomains.test(link.trim()) && !id) {\n    const paths = parsed.pathname.split('/');\n    id = parsed.host === 'youtu.be' ? paths[1] : paths[2];\n  } else if (parsed.hostname && !validQueryDomains.has(parsed.hostname)) {\n    throw Error('Not a YouTube domain');\n  }\n  if (!id) {\n    throw Error(`No video id found: \"${link}\"`);\n  }\n  id = id.substring(0, 11);\n  if (!exports.validateID(id)) {\n    throw TypeError(`Video id (${id}) does not match expected ` +\n      `format (${idRegex.toString()})`);\n  }\n  return id;\n};\n\n\n/**\n * Gets video ID either from a url or by checking if the given string\n * matches the video ID format.\n *\n * @param {string} str\n * @returns {string}\n * @throws {Error} If unable to find a id\n * @throws {TypeError} If videoid doesn't match specs\n */\nconst urlRegex = /^https?:\\/\\//;\nexports.getVideoID = str => {\n  if (exports.validateID(str)) {\n    return str;\n  } else if (urlRegex.test(str.trim())) {\n    return exports.getURLVideoID(str);\n  } else {\n    throw Error(`No video id found: ${str}`);\n  }\n};\n\n\n/**\n * Returns true if given id satifies YouTube's id format.\n *\n * @param {string} id\n * @return {boolean}\n */\nconst idRegex = /^[a-zA-Z0-9-_]{11}$/;\nexports.validateID = id => idRegex.test(id.trim());\n\n\n/**\n * Checks wether the input string includes a valid id.\n *\n * @param {string} string\n * @returns {boolean}\n */\nexports.validateURL = string => {\n  try {\n    exports.getURLVideoID(string);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveXRkbC1jb3JlL2xpYi91cmwtdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWixZQUFZLE9BQU87QUFDbkIsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxLQUFLO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxHQUFHO0FBQ3BDLGlCQUFpQixtQkFBbUI7QUFDcEM7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2IsWUFBWSxPQUFPO0FBQ25CLFlBQVksV0FBVztBQUN2QjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0osc0NBQXNDLElBQUk7QUFDMUM7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNBLGdDQUFnQyxHQUFHO0FBQ25DLGtCQUFrQjs7O0FBR2xCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFlvdVR1YmUgRG93bmxvYWRlclxceW91dHViZS1kb3dubG9hZGVyXFxub2RlX21vZHVsZXNcXHl0ZGwtY29yZVxcbGliXFx1cmwtdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBHZXQgdmlkZW8gSUQuXG4gKlxuICogVGhlcmUgYXJlIGEgZmV3IHR5cGUgb2YgdmlkZW8gVVJMIGZvcm1hdHMuXG4gKiAgLSBodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PVZJREVPX0lEXG4gKiAgLSBodHRwczovL20ueW91dHViZS5jb20vd2F0Y2g/dj1WSURFT19JRFxuICogIC0gaHR0cHM6Ly95b3V0dS5iZS9WSURFT19JRFxuICogIC0gaHR0cHM6Ly93d3cueW91dHViZS5jb20vdi9WSURFT19JRFxuICogIC0gaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvVklERU9fSURcbiAqICAtIGh0dHBzOi8vbXVzaWMueW91dHViZS5jb20vd2F0Y2g/dj1WSURFT19JRFxuICogIC0gaHR0cHM6Ly9nYW1pbmcueW91dHViZS5jb20vd2F0Y2g/dj1WSURFT19JRFxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBsaW5rXG4gKiBAcmV0dXJuIHtzdHJpbmd9XG4gKiBAdGhyb3dzIHtFcnJvcn0gSWYgdW5hYmxlIHRvIGZpbmQgYSBpZFxuICogQHRocm93cyB7VHlwZUVycm9yfSBJZiB2aWRlb2lkIGRvZXNuJ3QgbWF0Y2ggc3BlY3NcbiAqL1xuY29uc3QgdmFsaWRRdWVyeURvbWFpbnMgPSBuZXcgU2V0KFtcbiAgJ3lvdXR1YmUuY29tJyxcbiAgJ3d3dy55b3V0dWJlLmNvbScsXG4gICdtLnlvdXR1YmUuY29tJyxcbiAgJ211c2ljLnlvdXR1YmUuY29tJyxcbiAgJ2dhbWluZy55b3V0dWJlLmNvbScsXG5dKTtcbmNvbnN0IHZhbGlkUGF0aERvbWFpbnMgPSAvXmh0dHBzPzpcXC9cXC8oeW91dHVcXC5iZVxcL3wod3d3XFwuKT95b3V0dWJlXFwuY29tXFwvKGVtYmVkfHZ8c2hvcnRzKVxcLykvO1xuZXhwb3J0cy5nZXRVUkxWaWRlb0lEID0gbGluayA9PiB7XG4gIGNvbnN0IHBhcnNlZCA9IG5ldyBVUkwobGluay50cmltKCkpO1xuICBsZXQgaWQgPSBwYXJzZWQuc2VhcmNoUGFyYW1zLmdldCgndicpO1xuICBpZiAodmFsaWRQYXRoRG9tYWlucy50ZXN0KGxpbmsudHJpbSgpKSAmJiAhaWQpIHtcbiAgICBjb25zdCBwYXRocyA9IHBhcnNlZC5wYXRobmFtZS5zcGxpdCgnLycpO1xuICAgIGlkID0gcGFyc2VkLmhvc3QgPT09ICd5b3V0dS5iZScgPyBwYXRoc1sxXSA6IHBhdGhzWzJdO1xuICB9IGVsc2UgaWYgKHBhcnNlZC5ob3N0bmFtZSAmJiAhdmFsaWRRdWVyeURvbWFpbnMuaGFzKHBhcnNlZC5ob3N0bmFtZSkpIHtcbiAgICB0aHJvdyBFcnJvcignTm90IGEgWW91VHViZSBkb21haW4nKTtcbiAgfVxuICBpZiAoIWlkKSB7XG4gICAgdGhyb3cgRXJyb3IoYE5vIHZpZGVvIGlkIGZvdW5kOiBcIiR7bGlua31cImApO1xuICB9XG4gIGlkID0gaWQuc3Vic3RyaW5nKDAsIDExKTtcbiAgaWYgKCFleHBvcnRzLnZhbGlkYXRlSUQoaWQpKSB7XG4gICAgdGhyb3cgVHlwZUVycm9yKGBWaWRlbyBpZCAoJHtpZH0pIGRvZXMgbm90IG1hdGNoIGV4cGVjdGVkIGAgK1xuICAgICAgYGZvcm1hdCAoJHtpZFJlZ2V4LnRvU3RyaW5nKCl9KWApO1xuICB9XG4gIHJldHVybiBpZDtcbn07XG5cblxuLyoqXG4gKiBHZXRzIHZpZGVvIElEIGVpdGhlciBmcm9tIGEgdXJsIG9yIGJ5IGNoZWNraW5nIGlmIHRoZSBnaXZlbiBzdHJpbmdcbiAqIG1hdGNoZXMgdGhlIHZpZGVvIElEIGZvcm1hdC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogQHRocm93cyB7RXJyb3J9IElmIHVuYWJsZSB0byBmaW5kIGEgaWRcbiAqIEB0aHJvd3Mge1R5cGVFcnJvcn0gSWYgdmlkZW9pZCBkb2Vzbid0IG1hdGNoIHNwZWNzXG4gKi9cbmNvbnN0IHVybFJlZ2V4ID0gL15odHRwcz86XFwvXFwvLztcbmV4cG9ydHMuZ2V0VmlkZW9JRCA9IHN0ciA9PiB7XG4gIGlmIChleHBvcnRzLnZhbGlkYXRlSUQoc3RyKSkge1xuICAgIHJldHVybiBzdHI7XG4gIH0gZWxzZSBpZiAodXJsUmVnZXgudGVzdChzdHIudHJpbSgpKSkge1xuICAgIHJldHVybiBleHBvcnRzLmdldFVSTFZpZGVvSUQoc3RyKTtcbiAgfSBlbHNlIHtcbiAgICB0aHJvdyBFcnJvcihgTm8gdmlkZW8gaWQgZm91bmQ6ICR7c3RyfWApO1xuICB9XG59O1xuXG5cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIGdpdmVuIGlkIHNhdGlmaWVzIFlvdVR1YmUncyBpZCBmb3JtYXQuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGlkXG4gKiBAcmV0dXJuIHtib29sZWFufVxuICovXG5jb25zdCBpZFJlZ2V4ID0gL15bYS16QS1aMC05LV9dezExfSQvO1xuZXhwb3J0cy52YWxpZGF0ZUlEID0gaWQgPT4gaWRSZWdleC50ZXN0KGlkLnRyaW0oKSk7XG5cblxuLyoqXG4gKiBDaGVja3Mgd2V0aGVyIHRoZSBpbnB1dCBzdHJpbmcgaW5jbHVkZXMgYSB2YWxpZCBpZC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyaW5nXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0cy52YWxpZGF0ZVVSTCA9IHN0cmluZyA9PiB7XG4gIHRyeSB7XG4gICAgZXhwb3J0cy5nZXRVUkxWaWRlb0lEKHN0cmluZyk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/url-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/lib/utils.js":
/*!*********************************************!*\
  !*** ./node_modules/ytdl-core/lib/utils.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const miniget = __webpack_require__(/*! miniget */ \"(rsc)/./node_modules/miniget/dist/index.js\");\n\n\n/**\n * Extract string inbetween another.\n *\n * @param {string} haystack\n * @param {string} left\n * @param {string} right\n * @returns {string}\n */\nexports.between = (haystack, left, right) => {\n  let pos;\n  if (left instanceof RegExp) {\n    const match = haystack.match(left);\n    if (!match) { return ''; }\n    pos = match.index + match[0].length;\n  } else {\n    pos = haystack.indexOf(left);\n    if (pos === -1) { return ''; }\n    pos += left.length;\n  }\n  haystack = haystack.slice(pos);\n  pos = haystack.indexOf(right);\n  if (pos === -1) { return ''; }\n  haystack = haystack.slice(0, pos);\n  return haystack;\n};\n\n\n/**\n * Get a number from an abbreviated number string.\n *\n * @param {string} string\n * @returns {number}\n */\nexports.parseAbbreviatedNumber = string => {\n  const match = string\n    .replace(',', '.')\n    .replace(' ', '')\n    .match(/([\\d,.]+)([MK]?)/);\n  if (match) {\n    let [, num, multi] = match;\n    num = parseFloat(num);\n    return Math.round(multi === 'M' ? num * 1000000 :\n      multi === 'K' ? num * 1000 : num);\n  }\n  return null;\n};\n\n/**\n * Escape sequences for cutAfterJS\n * @param {string} start the character string the escape sequence\n * @param {string} end the character string to stop the escape seequence\n * @param {undefined|Regex} startPrefix a regex to check against the preceding 10 characters\n */\nconst ESCAPING_SEQUENZES = [\n  // Strings\n  { start: '\"', end: '\"' },\n  { start: \"'\", end: \"'\" },\n  { start: '`', end: '`' },\n  // RegeEx\n  { start: '/', end: '/', startPrefix: /(^|[[{:;,/])\\s?$/ },\n];\n\n/**\n * Match begin and end braces of input JS, return only JS\n *\n * @param {string} mixedJson\n * @returns {string}\n*/\nexports.cutAfterJS = mixedJson => {\n  // Define the general open and closing tag\n  let open, close;\n  if (mixedJson[0] === '[') {\n    open = '[';\n    close = ']';\n  } else if (mixedJson[0] === '{') {\n    open = '{';\n    close = '}';\n  }\n\n  if (!open) {\n    throw new Error(`Can't cut unsupported JSON (need to begin with [ or { ) but got: ${mixedJson[0]}`);\n  }\n\n  // States if the loop is currently inside an escaped js object\n  let isEscapedObject = null;\n\n  // States if the current character is treated as escaped or not\n  let isEscaped = false;\n\n  // Current open brackets to be closed\n  let counter = 0;\n\n  let i;\n  // Go through all characters from the start\n  for (i = 0; i < mixedJson.length; i++) {\n    // End of current escaped object\n    if (!isEscaped && isEscapedObject !== null && mixedJson[i] === isEscapedObject.end) {\n      isEscapedObject = null;\n      continue;\n    // Might be the start of a new escaped object\n    } else if (!isEscaped && isEscapedObject === null) {\n      for (const escaped of ESCAPING_SEQUENZES) {\n        if (mixedJson[i] !== escaped.start) continue;\n        // Test startPrefix against last 10 characters\n        if (!escaped.startPrefix || mixedJson.substring(i - 10, i).match(escaped.startPrefix)) {\n          isEscapedObject = escaped;\n          break;\n        }\n      }\n      // Continue if we found a new escaped object\n      if (isEscapedObject !== null) {\n        continue;\n      }\n    }\n\n    // Toggle the isEscaped boolean for every backslash\n    // Reset for every regular character\n    isEscaped = mixedJson[i] === '\\\\' && !isEscaped;\n\n    if (isEscapedObject !== null) continue;\n\n    if (mixedJson[i] === open) {\n      counter++;\n    } else if (mixedJson[i] === close) {\n      counter--;\n    }\n\n    // All brackets have been closed, thus end of JSON is reached\n    if (counter === 0) {\n      // Return the cut JSON\n      return mixedJson.substring(0, i + 1);\n    }\n  }\n\n  // We ran through the whole string and ended up with an unclosed bracket\n  throw Error(\"Can't cut unsupported JSON (no matching closing bracket found)\");\n};\n\n\n/**\n * Checks if there is a playability error.\n *\n * @param {Object} player_response\n * @param {Array.<string>} statuses\n * @param {Error} ErrorType\n * @returns {!Error}\n */\nexports.playError = (player_response, statuses, ErrorType = Error) => {\n  let playability = player_response && player_response.playabilityStatus;\n  if (playability && statuses.includes(playability.status)) {\n    return new ErrorType(playability.reason || (playability.messages && playability.messages[0]));\n  }\n  return null;\n};\n\n/**\n * Does a miniget request and calls options.requestCallback if present\n *\n * @param {string} url the request url\n * @param {Object} options an object with optional requestOptions and requestCallback parameters\n * @param {Object} requestOptionsOverwrite overwrite of options.requestOptions\n * @returns {miniget.Stream}\n */\nexports.exposedMiniget = (url, options = {}, requestOptionsOverwrite) => {\n  const req = miniget(url, requestOptionsOverwrite || options.requestOptions);\n  if (typeof options.requestCallback === 'function') options.requestCallback(req);\n  return req;\n};\n\n/**\n * Temporary helper to help deprecating a few properties.\n *\n * @param {Object} obj\n * @param {string} prop\n * @param {Object} value\n * @param {string} oldPath\n * @param {string} newPath\n */\nexports.deprecate = (obj, prop, value, oldPath, newPath) => {\n  Object.defineProperty(obj, prop, {\n    get: () => {\n      console.warn(`\\`${oldPath}\\` will be removed in a near future release, ` +\n        `use \\`${newPath}\\` instead.`);\n      return value;\n    },\n  });\n};\n\n\n// Check for updates.\nconst pkg = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/ytdl-core/package.json\");\nconst UPDATE_INTERVAL = 1000 * 60 * 60 * 12;\nexports.lastUpdateCheck = 0;\nexports.checkForUpdates = () => {\n  if (!process.env.YTDL_NO_UPDATE && !pkg.version.startsWith('0.0.0-') &&\n    Date.now() - exports.lastUpdateCheck >= UPDATE_INTERVAL) {\n    exports.lastUpdateCheck = Date.now();\n    return miniget('https://api.github.com/repos/fent/node-ytdl-core/releases/latest', {\n      headers: { 'User-Agent': 'ytdl-core' },\n    }).text().then(response => {\n      if (JSON.parse(response).tag_name !== `v${pkg.version}`) {\n        console.warn('\\x1b[33mWARNING:\\x1B[0m ytdl-core is out of date! Update with \"npm install ytdl-core@latest\".');\n      }\n    }, err => {\n      console.warn('Error checking for updates:', err.message);\n      console.warn('You can disable this check by setting the `YTDL_NO_UPDATE` env variable.');\n    });\n  }\n  return null;\n};\n\n\n/**\n * Gets random IPv6 Address from a block\n *\n * @param {string} ip the IPv6 block in CIDR-Notation\n * @returns {string}\n */\nexports.getRandomIPv6 = ip => {\n  // Start with a fast Regex-Check\n  if (!isIPv6(ip)) throw Error('Invalid IPv6 format');\n  // Start by splitting and normalizing addr and mask\n  const [rawAddr, rawMask] = ip.split('/');\n  let base10Mask = parseInt(rawMask);\n  if (!base10Mask || base10Mask > 128 || base10Mask < 24) throw Error('Invalid IPv6 subnet');\n  const base10addr = normalizeIP(rawAddr);\n  // Get random addr to pad with\n  // using Math.random since we're not requiring high level of randomness\n  const randomAddr = new Array(8).fill(1).map(() => Math.floor(Math.random() * 0xffff));\n\n  // Merge base10addr with randomAddr\n  const mergedAddr = randomAddr.map((randomItem, idx) => {\n    // Calculate the amount of static bits\n    const staticBits = Math.min(base10Mask, 16);\n    // Adjust the bitmask with the staticBits\n    base10Mask -= staticBits;\n    // Calculate the bitmask\n    // lsb makes the calculation way more complicated\n    const mask = 0xffff - ((2 ** (16 - staticBits)) - 1);\n    // Combine base10addr and random\n    return (base10addr[idx] & mask) + (randomItem & (mask ^ 0xffff));\n  });\n  // Return new addr\n  return mergedAddr.map(x => x.toString('16')).join(':');\n};\n\n\n// eslint-disable-next-line max-len\nconst IPV6_REGEX = /^(([0-9a-f]{1,4}:)(:[0-9a-f]{1,4}){1,6}|([0-9a-f]{1,4}:){1,2}(:[0-9a-f]{1,4}){1,5}|([0-9a-f]{1,4}:){1,3}(:[0-9a-f]{1,4}){1,4}|([0-9a-f]{1,4}:){1,4}(:[0-9a-f]{1,4}){1,3}|([0-9a-f]{1,4}:){1,5}(:[0-9a-f]{1,4}){1,2}|([0-9a-f]{1,4}:){1,6}(:[0-9a-f]{1,4})|([0-9a-f]{1,4}:){1,7}(([0-9a-f]{1,4})|:))\\/(1[0-1]\\d|12[0-8]|\\d{1,2})$/;\n/**\n * Quick check for a valid IPv6\n * The Regex only accepts a subset of all IPv6 Addresses\n *\n * @param {string} ip the IPv6 block in CIDR-Notation to test\n * @returns {boolean} true if valid\n */\nconst isIPv6 = exports.isIPv6 = ip => IPV6_REGEX.test(ip);\n\n\n/**\n * Normalise an IP Address\n *\n * @param {string} ip the IPv6 Addr\n * @returns {number[]} the 8 parts of the IPv6 as Integers\n */\nconst normalizeIP = exports.normalizeIP = ip => {\n  // Split by fill position\n  const parts = ip.split('::').map(x => x.split(':'));\n  // Normalize start and end\n  const partStart = parts[0] || [];\n  const partEnd = parts[1] || [];\n  partEnd.reverse();\n  // Placeholder for full ip\n  const fullIP = new Array(8).fill(0);\n  // Fill in start and end parts\n  for (let i = 0; i < Math.min(partStart.length, 8); i++) {\n    fullIP[i] = parseInt(partStart[i], 16) || 0;\n  }\n  for (let i = 0; i < Math.min(partEnd.length, 8); i++) {\n    fullIP[7 - i] = parseInt(partEnd[i], 16) || 0;\n  }\n  return fullIP;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ytdl-core/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ytdl-core/package.json":
/*!*********************************************!*\
  !*** ./node_modules/ytdl-core/package.json ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"ytdl-core","description":"YouTube video downloader in pure javascript.","keywords":["youtube","video","download"],"version":"4.11.5","repository":{"type":"git","url":"git://github.com/fent/node-ytdl-core.git"},"author":"fent <<EMAIL>> (https://github.com/fent)","contributors":["Tobias Kutscha (https://github.com/TimeForANinja)","Andrew Kelley (https://github.com/andrewrk)","Mauricio Allende (https://github.com/mallendeo)","Rodrigo Altamirano (https://github.com/raltamirano)","Jim Buck (https://github.com/JimmyBoh)","Paweł Ruciński (https://github.com/Roki100)","Alexander Paolini (https://github.com/Million900o)"],"main":"./lib/index.js","types":"./typings/index.d.ts","files":["lib","typings"],"scripts":{"test":"nyc --reporter=lcov --reporter=text-summary npm run test:unit","test:unit":"mocha --ignore test/irl-test.js test/*-test.js --timeout 4000","test:irl":"mocha --timeout 16000 test/irl-test.js","lint":"eslint ./","lint:fix":"eslint --fix ./","lint:typings":"tslint typings/index.d.ts","lint:typings:fix":"tslint --fix typings/index.d.ts"},"dependencies":{"m3u8stream":"^0.8.6","miniget":"^4.2.2","sax":"^1.1.3"},"devDependencies":{"@types/node":"^13.1.0","assert-diff":"^3.0.1","dtslint":"^3.6.14","eslint":"^6.8.0","mocha":"^7.0.0","muk-require":"^1.2.0","nock":"^13.0.4","nyc":"^15.0.0","sinon":"^9.0.0","stream-equal":"~1.1.0","typescript":"^3.9.7"},"engines":{"node":">=12"},"license":"MIT"}');

/***/ })

};
;