"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-unix";
exports.ids = ["vendor-chunks/is-unix"];
exports.modules = {

/***/ "(rsc)/./node_modules/is-unix/index.js":
/*!***************************************!*\
  !*** ./node_modules/is-unix/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = (platform = '') => {\n  platform = platform.toLowerCase()\n  return (\n    [\n      'aix',\n      'android',\n      'darwin',\n      'freebsd',\n      'linux',\n      'openbsd',\n      'sunos'\n    ].indexOf(platform) !== -1\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXMtdW5peC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcWW91VHViZSBEb3dubG9hZGVyXFx5b3V0dWJlLWRvd25sb2FkZXJcXG5vZGVfbW9kdWxlc1xcaXMtdW5peFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gKHBsYXRmb3JtID0gJycpID0+IHtcbiAgcGxhdGZvcm0gPSBwbGF0Zm9ybS50b0xvd2VyQ2FzZSgpXG4gIHJldHVybiAoXG4gICAgW1xuICAgICAgJ2FpeCcsXG4gICAgICAnYW5kcm9pZCcsXG4gICAgICAnZGFyd2luJyxcbiAgICAgICdmcmVlYnNkJyxcbiAgICAgICdsaW51eCcsXG4gICAgICAnb3BlbmJzZCcsXG4gICAgICAnc3Vub3MnXG4gICAgXS5pbmRleE9mKHBsYXRmb3JtKSAhPT0gLTFcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/is-unix/index.js\n");

/***/ })

};
;