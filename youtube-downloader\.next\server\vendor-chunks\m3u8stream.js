"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/m3u8stream";
exports.ids = ["vendor-chunks/m3u8stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/m3u8stream/dist/dash-mpd-parser.js":
/*!*********************************************************!*\
  !*** ./node_modules/m3u8stream/dist/dash-mpd-parser.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst sax_1 = __importDefault(__webpack_require__(/*! sax */ \"(rsc)/./node_modules/sax/lib/sax.js\"));\nconst parse_time_1 = __webpack_require__(/*! ./parse-time */ \"(rsc)/./node_modules/m3u8stream/dist/parse-time.js\");\n/**\n * A wrapper around sax that emits segments.\n */\nclass DashMPDParser extends stream_1.Writable {\n    constructor(targetID) {\n        super();\n        this._parser = sax_1.default.createStream(false, { lowercase: true });\n        this._parser.on('error', this.destroy.bind(this));\n        let lastTag;\n        let currtime = 0;\n        let seq = 0;\n        let segmentTemplate;\n        let timescale, offset, duration, baseURL;\n        let timeline = [];\n        let getSegments = false;\n        let gotSegments = false;\n        let isStatic;\n        let treeLevel;\n        let periodStart;\n        const tmpl = (str) => {\n            const context = {\n                RepresentationID: targetID,\n                Number: seq,\n                Time: currtime,\n            };\n            return str.replace(/\\$(\\w+)\\$/g, (m, p1) => `${context[p1]}`);\n        };\n        this._parser.on('opentag', node => {\n            switch (node.name) {\n                case 'mpd':\n                    currtime =\n                        node.attributes.availabilitystarttime ?\n                            new Date(node.attributes.availabilitystarttime).getTime() : 0;\n                    isStatic = node.attributes.type !== 'dynamic';\n                    break;\n                case 'period':\n                    // Reset everything on <Period> tag.\n                    seq = 0;\n                    timescale = 1000;\n                    duration = 0;\n                    offset = 0;\n                    baseURL = [];\n                    treeLevel = 0;\n                    periodStart = parse_time_1.durationStr(node.attributes.start) || 0;\n                    break;\n                case 'segmentlist':\n                    seq = parseInt(node.attributes.startnumber) || seq;\n                    timescale = parseInt(node.attributes.timescale) || timescale;\n                    duration = parseInt(node.attributes.duration) || duration;\n                    offset = parseInt(node.attributes.presentationtimeoffset) || offset;\n                    break;\n                case 'segmenttemplate':\n                    segmentTemplate = node.attributes;\n                    seq = parseInt(node.attributes.startnumber) || seq;\n                    timescale = parseInt(node.attributes.timescale) || timescale;\n                    break;\n                case 'segmenttimeline':\n                case 'baseurl':\n                    lastTag = node.name;\n                    break;\n                case 's':\n                    timeline.push({\n                        duration: parseInt(node.attributes.d),\n                        repeat: parseInt(node.attributes.r),\n                        time: parseInt(node.attributes.t),\n                    });\n                    break;\n                case 'adaptationset':\n                case 'representation':\n                    treeLevel++;\n                    if (!targetID) {\n                        targetID = node.attributes.id;\n                    }\n                    getSegments = node.attributes.id === `${targetID}`;\n                    if (getSegments) {\n                        if (periodStart) {\n                            currtime += periodStart;\n                        }\n                        if (offset) {\n                            currtime -= offset / timescale * 1000;\n                        }\n                        this.emit('starttime', currtime);\n                    }\n                    break;\n                case 'initialization':\n                    if (getSegments) {\n                        this.emit('item', {\n                            url: baseURL.filter(s => !!s).join('') + node.attributes.sourceurl,\n                            seq: seq,\n                            init: true,\n                            duration: 0,\n                        });\n                    }\n                    break;\n                case 'segmenturl':\n                    if (getSegments) {\n                        gotSegments = true;\n                        let tl = timeline.shift();\n                        let segmentDuration = ((tl === null || tl === void 0 ? void 0 : tl.duration) || duration) / timescale * 1000;\n                        this.emit('item', {\n                            url: baseURL.filter(s => !!s).join('') + node.attributes.media,\n                            seq: seq++,\n                            duration: segmentDuration,\n                        });\n                        currtime += segmentDuration;\n                    }\n                    break;\n            }\n        });\n        const onEnd = () => {\n            if (isStatic) {\n                this.emit('endlist');\n            }\n            if (!getSegments) {\n                this.destroy(Error(`Representation '${targetID}' not found`));\n            }\n            else {\n                this.emit('end');\n            }\n        };\n        this._parser.on('closetag', tagName => {\n            switch (tagName) {\n                case 'adaptationset':\n                case 'representation':\n                    treeLevel--;\n                    if (segmentTemplate && timeline.length) {\n                        gotSegments = true;\n                        if (segmentTemplate.initialization) {\n                            this.emit('item', {\n                                url: baseURL.filter(s => !!s).join('') +\n                                    tmpl(segmentTemplate.initialization),\n                                seq: seq,\n                                init: true,\n                                duration: 0,\n                            });\n                        }\n                        for (let { duration: itemDuration, repeat, time } of timeline) {\n                            itemDuration = itemDuration / timescale * 1000;\n                            repeat = repeat || 1;\n                            currtime = time || currtime;\n                            for (let i = 0; i < repeat; i++) {\n                                this.emit('item', {\n                                    url: baseURL.filter(s => !!s).join('') +\n                                        tmpl(segmentTemplate.media),\n                                    seq: seq++,\n                                    duration: itemDuration,\n                                });\n                                currtime += itemDuration;\n                            }\n                        }\n                    }\n                    if (gotSegments) {\n                        this.emit('endearly');\n                        onEnd();\n                        this._parser.removeAllListeners();\n                        this.removeAllListeners('finish');\n                    }\n                    break;\n            }\n        });\n        this._parser.on('text', text => {\n            if (lastTag === 'baseurl') {\n                baseURL[treeLevel] = text;\n                lastTag = null;\n            }\n        });\n        this.on('finish', onEnd);\n    }\n    _write(chunk, encoding, callback) {\n        this._parser.write(chunk);\n        callback();\n    }\n}\nexports[\"default\"] = DashMPDParser;\n//# sourceMappingURL=dash-mpd-parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/m3u8stream/dist/dash-mpd-parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/m3u8stream/dist/index.js":
/*!***********************************************!*\
  !*** ./node_modules/m3u8stream/dist/index.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst miniget_1 = __importDefault(__webpack_require__(/*! miniget */ \"(rsc)/./node_modules/miniget/dist/index.js\"));\nconst m3u8_parser_1 = __importDefault(__webpack_require__(/*! ./m3u8-parser */ \"(rsc)/./node_modules/m3u8stream/dist/m3u8-parser.js\"));\nconst dash_mpd_parser_1 = __importDefault(__webpack_require__(/*! ./dash-mpd-parser */ \"(rsc)/./node_modules/m3u8stream/dist/dash-mpd-parser.js\"));\nconst queue_1 = __webpack_require__(/*! ./queue */ \"(rsc)/./node_modules/m3u8stream/dist/queue.js\");\nconst parse_time_1 = __webpack_require__(/*! ./parse-time */ \"(rsc)/./node_modules/m3u8stream/dist/parse-time.js\");\nconst supportedParsers = {\n    m3u8: m3u8_parser_1.default,\n    'dash-mpd': dash_mpd_parser_1.default,\n};\nlet m3u8stream = ((playlistURL, options = {}) => {\n    const stream = new stream_1.PassThrough({ highWaterMark: options.highWaterMark });\n    const chunkReadahead = options.chunkReadahead || 3;\n    // 20 seconds.\n    const liveBuffer = options.liveBuffer || 20000;\n    const requestOptions = options.requestOptions;\n    const Parser = supportedParsers[options.parser || (/\\.mpd$/.test(playlistURL) ? 'dash-mpd' : 'm3u8')];\n    if (!Parser) {\n        throw TypeError(`parser '${options.parser}' not supported`);\n    }\n    let begin = 0;\n    if (typeof options.begin !== 'undefined') {\n        begin = typeof options.begin === 'string' ?\n            parse_time_1.humanStr(options.begin) :\n            Math.max(options.begin - liveBuffer, 0);\n    }\n    const forwardEvents = (req) => {\n        for (let event of ['abort', 'request', 'response', 'redirect', 'retry', 'reconnect']) {\n            req.on(event, stream.emit.bind(stream, event));\n        }\n    };\n    let currSegment;\n    const streamQueue = new queue_1.Queue((req, callback) => {\n        currSegment = req;\n        // Count the size manually, since the `content-length` header is not\n        // always there.\n        let size = 0;\n        req.on('data', (chunk) => size += chunk.length);\n        req.pipe(stream, { end: false });\n        req.on('end', () => callback(null, size));\n    }, { concurrency: 1 });\n    let segmentNumber = 0;\n    let downloaded = 0;\n    const requestQueue = new queue_1.Queue((segment, callback) => {\n        let reqOptions = Object.assign({}, requestOptions);\n        if (segment.range) {\n            reqOptions.headers = Object.assign({}, reqOptions.headers, {\n                Range: `bytes=${segment.range.start}-${segment.range.end}`,\n            });\n        }\n        let req = miniget_1.default(new URL(segment.url, playlistURL).toString(), reqOptions);\n        req.on('error', callback);\n        forwardEvents(req);\n        streamQueue.push(req, (_, size) => {\n            downloaded += +size;\n            stream.emit('progress', {\n                num: ++segmentNumber,\n                size: size,\n                duration: segment.duration,\n                url: segment.url,\n            }, requestQueue.total, downloaded);\n            callback(null);\n        });\n    }, { concurrency: chunkReadahead });\n    const onError = (err) => {\n        stream.emit('error', err);\n        // Stop on any error.\n        stream.end();\n    };\n    // When to look for items again.\n    let refreshThreshold;\n    let minRefreshTime;\n    let refreshTimeout;\n    let fetchingPlaylist = true;\n    let ended = false;\n    let isStatic = false;\n    let lastRefresh;\n    const onQueuedEnd = (err) => {\n        currSegment = null;\n        if (err) {\n            onError(err);\n        }\n        else if (!fetchingPlaylist && !ended && !isStatic &&\n            requestQueue.tasks.length + requestQueue.active <= refreshThreshold) {\n            let ms = Math.max(0, minRefreshTime - (Date.now() - lastRefresh));\n            fetchingPlaylist = true;\n            refreshTimeout = setTimeout(refreshPlaylist, ms);\n        }\n        else if ((ended || isStatic) &&\n            !requestQueue.tasks.length && !requestQueue.active) {\n            stream.end();\n        }\n    };\n    let currPlaylist;\n    let lastSeq;\n    let starttime = 0;\n    const refreshPlaylist = () => {\n        lastRefresh = Date.now();\n        currPlaylist = miniget_1.default(playlistURL, requestOptions);\n        currPlaylist.on('error', onError);\n        forwardEvents(currPlaylist);\n        const parser = currPlaylist.pipe(new Parser(options.id));\n        parser.on('starttime', (a) => {\n            if (starttime) {\n                return;\n            }\n            starttime = a;\n            if (typeof options.begin === 'string' && begin >= 0) {\n                begin += starttime;\n            }\n        });\n        parser.on('endlist', () => { isStatic = true; });\n        parser.on('endearly', currPlaylist.unpipe.bind(currPlaylist, parser));\n        let addedItems = [];\n        const addItem = (item) => {\n            if (!item.init) {\n                if (item.seq <= lastSeq) {\n                    return;\n                }\n                lastSeq = item.seq;\n            }\n            begin = item.time;\n            requestQueue.push(item, onQueuedEnd);\n            addedItems.push(item);\n        };\n        let tailedItems = [], tailedItemsDuration = 0;\n        parser.on('item', (item) => {\n            let timedItem = Object.assign({ time: starttime }, item);\n            if (begin <= timedItem.time) {\n                addItem(timedItem);\n            }\n            else {\n                tailedItems.push(timedItem);\n                tailedItemsDuration += timedItem.duration;\n                // Only keep the last `liveBuffer` of items.\n                while (tailedItems.length > 1 &&\n                    tailedItemsDuration - tailedItems[0].duration > liveBuffer) {\n                    const lastItem = tailedItems.shift();\n                    tailedItemsDuration -= lastItem.duration;\n                }\n            }\n            starttime += timedItem.duration;\n        });\n        parser.on('end', () => {\n            currPlaylist = null;\n            // If we are too ahead of the stream, make sure to get the\n            // latest available items with a small buffer.\n            if (!addedItems.length && tailedItems.length) {\n                tailedItems.forEach(item => { addItem(item); });\n            }\n            // Refresh the playlist when remaining segments get low.\n            refreshThreshold = Math.max(1, Math.ceil(addedItems.length * 0.01));\n            // Throttle refreshing the playlist by looking at the duration\n            // of live items added on this refresh.\n            minRefreshTime =\n                addedItems.reduce((total, item) => item.duration + total, 0);\n            fetchingPlaylist = false;\n            onQueuedEnd(null);\n        });\n    };\n    refreshPlaylist();\n    stream.end = () => {\n        ended = true;\n        streamQueue.die();\n        requestQueue.die();\n        clearTimeout(refreshTimeout);\n        currPlaylist === null || currPlaylist === void 0 ? void 0 : currPlaylist.destroy();\n        currSegment === null || currSegment === void 0 ? void 0 : currSegment.destroy();\n        stream_1.PassThrough.prototype.end.call(stream, null);\n        return stream;\n    };\n    return stream;\n});\nm3u8stream.parseTimestamp = parse_time_1.humanStr;\nmodule.exports = m3u8stream;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/m3u8stream/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/m3u8stream/dist/m3u8-parser.js":
/*!*****************************************************!*\
  !*** ./node_modules/m3u8stream/dist/m3u8-parser.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\n/**\n * A very simple m3u8 playlist file parser that detects tags and segments.\n */\nclass m3u8Parser extends stream_1.Writable {\n    constructor() {\n        super();\n        this._lastLine = '';\n        this._seq = 0;\n        this._nextItemDuration = null;\n        this._nextItemRange = null;\n        this._lastItemRangeEnd = 0;\n        this.on('finish', () => {\n            this._parseLine(this._lastLine);\n            this.emit('end');\n        });\n    }\n    _parseAttrList(value) {\n        let attrs = {};\n        let regex = /([A-Z0-9-]+)=(?:\"([^\"]*?)\"|([^,]*?))/g;\n        let match;\n        while ((match = regex.exec(value)) !== null) {\n            attrs[match[1]] = match[2] || match[3];\n        }\n        return attrs;\n    }\n    _parseRange(value) {\n        if (!value)\n            return null;\n        let svalue = value.split('@');\n        let start = svalue[1] ? parseInt(svalue[1]) : this._lastItemRangeEnd + 1;\n        let end = start + parseInt(svalue[0]) - 1;\n        let range = { start, end };\n        this._lastItemRangeEnd = range.end;\n        return range;\n    }\n    _parseLine(line) {\n        let match = line.match(/^#(EXT[A-Z0-9-]+)(?::(.*))?/);\n        if (match) {\n            // This is a tag.\n            const tag = match[1];\n            const value = match[2] || '';\n            switch (tag) {\n                case 'EXT-X-PROGRAM-DATE-TIME':\n                    this.emit('starttime', new Date(value).getTime());\n                    break;\n                case 'EXT-X-MEDIA-SEQUENCE':\n                    this._seq = parseInt(value);\n                    break;\n                case 'EXT-X-MAP': {\n                    let attrs = this._parseAttrList(value);\n                    if (!attrs.URI) {\n                        this.destroy(new Error('`EXT-X-MAP` found without required attribute `URI`'));\n                        return;\n                    }\n                    this.emit('item', {\n                        url: attrs.URI,\n                        seq: this._seq,\n                        init: true,\n                        duration: 0,\n                        range: this._parseRange(attrs.BYTERANGE),\n                    });\n                    break;\n                }\n                case 'EXT-X-BYTERANGE': {\n                    this._nextItemRange = this._parseRange(value);\n                    break;\n                }\n                case 'EXTINF':\n                    this._nextItemDuration =\n                        Math.round(parseFloat(value.split(',')[0]) * 1000);\n                    break;\n                case 'EXT-X-ENDLIST':\n                    this.emit('endlist');\n                    break;\n            }\n        }\n        else if (!/^#/.test(line) && line.trim()) {\n            // This is a segment\n            this.emit('item', {\n                url: line.trim(),\n                seq: this._seq++,\n                duration: this._nextItemDuration,\n                range: this._nextItemRange,\n            });\n            this._nextItemRange = null;\n        }\n    }\n    _write(chunk, encoding, callback) {\n        let lines = chunk.toString('utf8').split('\\n');\n        if (this._lastLine) {\n            lines[0] = this._lastLine + lines[0];\n        }\n        lines.forEach((line, i) => {\n            if (this.destroyed)\n                return;\n            if (i < lines.length - 1) {\n                this._parseLine(line);\n            }\n            else {\n                // Save the last line in case it has been broken up.\n                this._lastLine = line;\n            }\n        });\n        callback();\n    }\n}\nexports[\"default\"] = m3u8Parser;\n//# sourceMappingURL=m3u8-parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/m3u8stream/dist/m3u8-parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/m3u8stream/dist/parse-time.js":
/*!****************************************************!*\
  !*** ./node_modules/m3u8stream/dist/parse-time.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.durationStr = exports.humanStr = void 0;\nconst numberFormat = /^\\d+$/;\nconst timeFormat = /^(?:(?:(\\d+):)?(\\d{1,2}):)?(\\d{1,2})(?:\\.(\\d{3}))?$/;\nconst timeUnits = {\n    ms: 1,\n    s: 1000,\n    m: 60000,\n    h: 3600000,\n};\n/**\n * Converts human friendly time to milliseconds. Supports the format\n * 00:00:00.000 for hours, minutes, seconds, and milliseconds respectively.\n * And 0ms, 0s, 0m, 0h, and together 1m1s.\n *\n * @param {number|string} time\n * @returns {number}\n */\nexports.humanStr = (time) => {\n    if (typeof time === 'number') {\n        return time;\n    }\n    if (numberFormat.test(time)) {\n        return +time;\n    }\n    const firstFormat = timeFormat.exec(time);\n    if (firstFormat) {\n        return (+(firstFormat[1] || 0) * timeUnits.h) +\n            (+(firstFormat[2] || 0) * timeUnits.m) +\n            (+firstFormat[3] * timeUnits.s) +\n            +(firstFormat[4] || 0);\n    }\n    else {\n        let total = 0;\n        const r = /(-?\\d+)(ms|s|m|h)/g;\n        let rs;\n        while ((rs = r.exec(time)) !== null) {\n            total += +rs[1] * timeUnits[rs[2]];\n        }\n        return total;\n    }\n};\n/**\n * Parses a duration string in the form of \"123.456S\", returns milliseconds.\n *\n * @param {string} time\n * @returns {number}\n */\nexports.durationStr = (time) => {\n    let total = 0;\n    const r = /(\\d+(?:\\.\\d+)?)(S|M|H)/g;\n    let rs;\n    while ((rs = r.exec(time)) !== null) {\n        total += +rs[1] * timeUnits[rs[2].toLowerCase()];\n    }\n    return total;\n};\n//# sourceMappingURL=parse-time.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/m3u8stream/dist/parse-time.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/m3u8stream/dist/queue.js":
/*!***********************************************!*\
  !*** ./node_modules/m3u8stream/dist/queue.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Queue = void 0;\nclass Queue {\n    /**\n     * A really simple queue with concurrency.\n     *\n     * @param {Function} worker\n     * @param {Object} options\n     * @param {!number} options.concurrency\n     */\n    constructor(worker, options = {}) {\n        this._worker = worker;\n        this._concurrency = options.concurrency || 1;\n        this.tasks = [];\n        this.total = 0;\n        this.active = 0;\n    }\n    /**\n     * Push a task to the queue.\n     *\n     *  @param {T} item\n     *  @param {!Function} callback\n     */\n    push(item, callback) {\n        this.tasks.push({ item, callback });\n        this.total++;\n        this._next();\n    }\n    /**\n     * Process next job in queue.\n     */\n    _next() {\n        if (this.active >= this._concurrency || !this.tasks.length) {\n            return;\n        }\n        const { item, callback } = this.tasks.shift();\n        let callbackCalled = false;\n        this.active++;\n        this._worker(item, (err, result) => {\n            if (callbackCalled) {\n                return;\n            }\n            this.active--;\n            callbackCalled = true;\n            callback === null || callback === void 0 ? void 0 : callback(err, result);\n            this._next();\n        });\n    }\n    /**\n     * Stops processing queued jobs.\n     */\n    die() {\n        this.tasks = [];\n    }\n}\nexports.Queue = Queue;\n//# sourceMappingURL=queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/m3u8stream/dist/queue.js\n");

/***/ })

};
;