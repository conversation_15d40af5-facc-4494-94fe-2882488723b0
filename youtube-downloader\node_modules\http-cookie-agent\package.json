{"name": "http-cookie-agent", "version": "6.0.8", "description": "Allows cookies with every Node.js HTTP clients.", "keywords": ["agent", "axios", "cookies", "fetch", "got", "http", "https", "needle", "node-fetch", "phin", "request", "superagent", "tough-cookie", "urllib", "undici"], "homepage": "https://github.com/3846masa/http-cookie-agent#readme", "bugs": {"url": "https://github.com/3846masa/http-cookie-agent/issues"}, "repository": {"type": "git", "url": "https://github.com/3846masa/http-cookie-agent.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "exports": {"./http": "./http/index.js", "./undici": "./undici/index.js"}, "files": ["dist", "undici", "http", "!**/__tests__"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "pnpm run --parallel \"/^build:.*/\"", "build:cjs": "babel src --out-dir dist --extensions .ts --out-file-extension .js", "build:mjs": "babel src --out-dir dist --extensions .mts --out-file-extension .mjs", "format": "pnpm run --sequential \"/^format:.*/\"", "format:eslint": "eslint --fix .", "format:prettier": "prettier --write .", "lint": "pnpm run \"/^lint:.*/\"", "lint:dts": "tsc --noEmit http/*.d.ts undici/*.d.ts", "lint:eslint": "eslint --max-warnings 0 .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "test": "NODE_OPTIONS=\"--experimental-vm-modules\" jest"}, "dependencies": {"agent-base": "^7.1.3"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#8ad26d06fa6071bac299dcc44635329adf69e1e0", "@babel/cli": "7.26.4", "@babel/core": "7.26.0", "@babel/plugin-proposal-explicit-resource-management": "7.25.9", "@babel/preset-env": "7.26.0", "@babel/preset-typescript": "7.26.0", "@hapi/wreck": "18.1.0", "@jest/globals": "29.7.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/babel__core": "7.20.5", "@types/needle": "3.3.0", "@types/node": "18.19.68", "@types/request": "2.48.12", "@types/semver": "7.5.8", "@types/superagent": "8.1.9", "agentkeepalive": "4.5.0", "axios": "1.7.9", "babel-jest": "29.7.0", "disposablestack": "1.1.6", "got": "12.6.1", "http-proxy-agent": "7.0.2", "jest": "29.7.0", "needle": "3.3.1", "node-fetch": "3.3.2", "phin": "3.7.1", "proxy": "2.2.0", "request": "2.88.2", "rimraf": "5.0.10", "semantic-release": "22.0.12", "semver": "7.6.3", "superagent": "10.1.1", "tough-cookie": "5.0.0", "typescript": "5.7.2", "undici": "6.21.0", "urllib": "4.4.0"}, "peerDependencies": {"tough-cookie": "^4.0.0 || ^5.0.0", "undici": "^5.11.0 || ^6.0.0"}, "peerDependenciesMeta": {"undici": {"optional": true}}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=18.0.0"}, "pnpm": {"overrides": {"urllib>undici": "$undici"}, "patchedDependencies": {"@semantic-release/git@10.0.1": "patches/@<EMAIL>"}}}